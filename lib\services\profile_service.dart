import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:mr_garments_mobile/services/session_service.dart';

class ProfileService {
  static const String baseUrl = 'https://mrgindia.com/api';

  /// Update user profile information
  static Future<Map<String, dynamic>> updateProfile({
    required String name,
    required String email,
    String? mobileNumber,
    String? address,
    String? profileImageUrl,
  }) async {
    try {
      final token = await SessionService.getAuthToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.put(
        Uri.parse('$baseUrl/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'name': name,
          'email': email,
          if (mobileNumber != null) 'mobile_number': mobileNumber,
          if (address != null) 'address': address,
          if (profileImageUrl != null) 'profile_image_url': profileImageUrl,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        // Update session data with new user information
        if (data['user'] != null) {
          await SessionService.updateUserData(data['user']);
        }
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update profile');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// Upload profile image
  static Future<String> uploadProfileImage(File imageFile) async {
    try {
      final token = await SessionService.getAuthToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/profile/upload-image'),
      );

      request.headers['Authorization'] = 'Bearer $token';
      request.files.add(
        await http.MultipartFile.fromPath('image', imageFile.path),
      );

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return data['image_url'] ?? data['url'] ?? '';
      } else {
        throw Exception(data['message'] ?? 'Failed to upload image');
      }
    } catch (e) {
      throw Exception('Image upload error: $e');
    }
  }

  /// Change user password
  static Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    try {
      final token = await SessionService.getAuthToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.put(
        Uri.parse('$baseUrl/profile/change-password'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'current_password': currentPassword,
          'new_password': newPassword,
          'new_password_confirmation': newPasswordConfirmation,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to change password');
      }
    } catch (e) {
      throw Exception('Password change error: $e');
    }
  }

  /// Get current user profile
  static Future<Map<String, dynamic>> getCurrentProfile() async {
    try {
      final token = await SessionService.getAuthToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse('$baseUrl/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        // Update session data with fresh user information
        if (data['user'] != null) {
          await SessionService.updateUserData(data['user']);
        }
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to fetch profile');
      }
    } catch (e) {
      throw Exception('Profile fetch error: $e');
    }
  }
}
