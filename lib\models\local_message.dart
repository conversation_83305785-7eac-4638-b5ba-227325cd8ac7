import 'package:hive/hive.dart';
import 'package:mr_garments_mobile/models/message.dart';

part 'local_message.g.dart';

/// Local message model for Hive database storage
/// This model is used for offline chat functionality
@HiveType(typeId: 0)
class LocalMessage extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String senderId;

  @HiveField(2)
  final String senderName;

  @HiveField(3)
  final String? senderProfileUrl;

  @HiveField(4)
  final String chatId; // Chat ID this message belongs to

  @HiveField(5)
  final int messageType; // MessageType enum as int

  @HiveField(6)
  final String? text;

  @HiveField(7)
  final String? mediaUrl; // Firebase URL

  @HiveField(8)
  final String? localPath; // Local file path for offline access

  @HiveField(9)
  final String? fileName;

  @HiveField(10)
  final int? fileSize;

  @HiveField(11)
  final String? thumbnailUrl; // Firebase thumbnail URL

  @HiveField(12)
  final String? localThumbnailPath; // Local thumbnail path

  @HiveField(13)
  final int messageStatus; // MessageStatus enum as int

  @HiveField(14)
  final int timestamp; // DateTime as milliseconds since epoch

  @HiveField(15)
  final String? replyToMessageId;

  @HiveField(16)
  final String? replyToText;

  @HiveField(17)
  final String? replyToSenderName;

  @HiveField(18)
  final bool isForwarded;

  @HiveField(19)
  final List<String> readBy;

  @HiveField(20)
  final List<String> deletedBy;

  @HiveField(21)
  final String? metadataJson; // JSON string of metadata

  @HiveField(22)
  final bool isSent; // Whether message was successfully sent to Firebase

  @HiveField(23)
  final bool isLocalOnly; // Whether message exists only locally (not synced)

  @HiveField(24)
  final int? optimisticStatus; // OptimisticMessageStatus enum as int

  @HiveField(25)
  final int? uploadStatus; // UploadStatus enum as int

  @HiveField(26)
  final double? uploadProgress; // Upload progress 0.0 to 1.0

  @HiveField(27)
  final int createdAt; // Local creation timestamp

  @HiveField(28)
  final int updatedAt; // Last update timestamp

  LocalMessage({
    required this.id,
    required this.senderId,
    required this.senderName,
    this.senderProfileUrl,
    required this.chatId,
    required this.messageType,
    this.text,
    this.mediaUrl,
    this.localPath,
    this.fileName,
    this.fileSize,
    this.thumbnailUrl,
    this.localThumbnailPath,
    required this.messageStatus,
    required this.timestamp,
    this.replyToMessageId,
    this.replyToText,
    this.replyToSenderName,
    this.isForwarded = false,
    this.readBy = const [],
    this.deletedBy = const [],
    this.metadataJson,
    this.isSent = false,
    this.isLocalOnly = false,
    this.optimisticStatus,
    this.uploadStatus,
    this.uploadProgress,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Convert from Firebase Message to LocalMessage
  factory LocalMessage.fromMessage(Message message, String chatId) {
    final now = DateTime.now().millisecondsSinceEpoch;
    return LocalMessage(
      id: message.id,
      senderId: message.senderId,
      senderName: message.senderName,
      senderProfileUrl: message.senderProfileUrl,
      chatId: chatId,
      messageType: message.type.index,
      text: message.text,
      mediaUrl: message.mediaUrl,
      localPath: message.localImagePath,
      fileName: message.fileName,
      fileSize: message.fileSize,
      thumbnailUrl: message.thumbnailUrl,
      localThumbnailPath: message.localThumbnailPath,
      messageStatus: message.status.index,
      timestamp: message.timestamp.millisecondsSinceEpoch,
      replyToMessageId: message.replyToMessageId,
      replyToText: message.replyToText,
      replyToSenderName: message.replyToSenderName,
      isForwarded: message.isForwarded,
      readBy: List<String>.from(message.readBy),
      deletedBy: List<String>.from(message.deletedBy),
      metadataJson:
          message.metadata != null ? _encodeMetadata(message.metadata!) : null,
      isSent:
          message.status == MessageStatus.sent ||
          message.status == MessageStatus.delivered ||
          message.status == MessageStatus.read,
      isLocalOnly: message.isLocalOnly,
      optimisticStatus: message.optimisticStatus?.index,
      uploadStatus: message.uploadStatus?.index,
      uploadProgress: message.uploadProgress,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Convert LocalMessage to Firebase Message
  Message toMessage() {
    return Message(
      id: id,
      senderId: senderId,
      senderName: senderName,
      senderProfileUrl: senderProfileUrl,
      type: MessageType.values[messageType],
      text: text,
      mediaUrl: mediaUrl,
      fileName: fileName,
      fileSize: fileSize,
      thumbnailUrl: thumbnailUrl,
      status: MessageStatus.values[messageStatus],
      timestamp: DateTime.fromMillisecondsSinceEpoch(timestamp),
      replyToMessageId: replyToMessageId,
      replyToText: replyToText,
      replyToSenderName: replyToSenderName,
      isForwarded: isForwarded,
      readBy: List<String>.from(readBy),
      deletedBy: List<String>.from(deletedBy),
      metadata: metadataJson != null ? _decodeMetadata(metadataJson!) : null,
      localImagePath: localPath,
      localThumbnailPath: localThumbnailPath,
      optimisticStatus:
          optimisticStatus != null
              ? OptimisticMessageStatus.values[optimisticStatus!]
              : null,
      uploadStatus:
          uploadStatus != null ? UploadStatus.values[uploadStatus!] : null,
      uploadProgress: uploadProgress,
      isLocalOnly: isLocalOnly,
    );
  }

  /// Create a copy with updated fields
  LocalMessage copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? senderProfileUrl,
    String? chatId,
    int? messageType,
    String? text,
    String? mediaUrl,
    String? localPath,
    String? fileName,
    int? fileSize,
    String? thumbnailUrl,
    String? localThumbnailPath,
    int? messageStatus,
    int? timestamp,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    bool? isForwarded,
    List<String>? readBy,
    List<String>? deletedBy,
    String? metadataJson,
    bool? isSent,
    bool? isLocalOnly,
    int? optimisticStatus,
    int? uploadStatus,
    double? uploadProgress,
    int? createdAt,
    int? updatedAt,
  }) {
    return LocalMessage(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderProfileUrl: senderProfileUrl ?? this.senderProfileUrl,
      chatId: chatId ?? this.chatId,
      messageType: messageType ?? this.messageType,
      text: text ?? this.text,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      localPath: localPath ?? this.localPath,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      localThumbnailPath: localThumbnailPath ?? this.localThumbnailPath,
      messageStatus: messageStatus ?? this.messageStatus,
      timestamp: timestamp ?? this.timestamp,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      replyToText: replyToText ?? this.replyToText,
      replyToSenderName: replyToSenderName ?? this.replyToSenderName,
      isForwarded: isForwarded ?? this.isForwarded,
      readBy: readBy ?? this.readBy,
      deletedBy: deletedBy ?? this.deletedBy,
      metadataJson: metadataJson ?? this.metadataJson,
      isSent: isSent ?? this.isSent,
      isLocalOnly: isLocalOnly ?? this.isLocalOnly,
      optimisticStatus: optimisticStatus ?? this.optimisticStatus,
      uploadStatus: uploadStatus ?? this.uploadStatus,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// Helper method to encode metadata to JSON string
  static String _encodeMetadata(Map<String, dynamic> metadata) {
    try {
      // Convert to JSON string for proper storage
      final jsonString = metadata.entries
          .map((e) {
            final value = e.value;
            if (value is String) {
              return '"${e.key}":"${value.replaceAll('"', '\\"')}"';
            } else if (value is bool) {
              return '"${e.key}":${value.toString()}';
            } else if (value is num) {
              return '"${e.key}":${value.toString()}';
            } else {
              return '"${e.key}":"${value.toString()}"';
            }
          })
          .join(',');
      return '{$jsonString}';
    } catch (e) {
      return '{}';
    }
  }

  /// Helper method to decode metadata from JSON string
  static Map<String, dynamic>? _decodeMetadata(String metadataJson) {
    try {
      if (metadataJson.isEmpty || metadataJson == '{}') return null;

      // Simple JSON parsing for basic types
      final Map<String, dynamic> result = {};

      // Remove braces and split by comma
      final content = metadataJson.substring(1, metadataJson.length - 1);
      if (content.isEmpty) return null;

      final pairs = content.split(',');
      for (final pair in pairs) {
        final colonIndex = pair.indexOf(':');
        if (colonIndex == -1) continue;

        final key = pair.substring(0, colonIndex).trim().replaceAll('"', '');
        final valueStr = pair.substring(colonIndex + 1).trim();

        // Parse value based on type
        dynamic value;
        if (valueStr == 'true') {
          value = true;
        } else if (valueStr == 'false') {
          value = false;
        } else if (valueStr.startsWith('"') && valueStr.endsWith('"')) {
          value = valueStr.substring(1, valueStr.length - 1);
        } else if (RegExp(r'^\d+$').hasMatch(valueStr)) {
          value = int.tryParse(valueStr) ?? valueStr;
        } else if (RegExp(r'^\d+\.\d+$').hasMatch(valueStr)) {
          value = double.tryParse(valueStr) ?? valueStr;
        } else {
          value = valueStr;
        }

        result[key] = value;
      }

      return result.isNotEmpty ? result : null;
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'LocalMessage(id: $id, chatId: $chatId, type: $messageType, isSent: $isSent, isLocalOnly: $isLocalOnly)';
  }
}
