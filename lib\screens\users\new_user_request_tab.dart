import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/widgets/network_error_widget.dart';
import 'package:mr_garments_mobile/providers/user_provider.dart';
import 'package:mr_garments_mobile/providers/generic_retailer_provider.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class NewUserRequestTab extends ConsumerStatefulWidget {
  const NewUserRequestTab({super.key});

  @override
  ConsumerState<NewUserRequestTab> createState() => _NewUserRequestTabState();
}

class _NewUserRequestTabState extends ConsumerState<NewUserRequestTab> {
  @override
  void initState() {
    super.initState();
    // Fetch both user requests and retailer requests when the widget is first initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(usersProvider.notifier).fetchNewUserRequests();
      ref.read(genericRetailerProvider.notifier).fetchRetailerRequests();
    });
  }

  void _handleAction(
    BuildContext context,
    WidgetRef ref,
    int userId,
    String action,
  ) async {
    // showDialog(
    //   context: context,
    //   barrierDismissible: false,
    //   builder: (_) => const Center(child: CircularProgressIndicator()),
    // );

    try {
      await ref.read(usersProvider.notifier).verifyUser(userId, action);

      // Refresh the new user requests list to remove the processed request
      await ref.read(usersProvider.notifier).fetchNewUserRequests();

      if (!context.mounted) return;
      Navigator.pop(context);
      AppSnackbar.showSuccess(
        context,
        "User ${action == 'approve' ? 'approved' : 'rejected'} successfully",
      );
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context);
        AppSnackbar.showError(context, 'Error: ${e.toString()}');
      }
    }
  }

  void _handleRetailerAction(
    BuildContext context,
    WidgetRef ref,
    int retailerId,
    String action,
  ) async {
    // showDialog(
    //   context: context,
    //   barrierDismissible: false,
    //   builder: (_) => const Center(child: CircularProgressIndicator()),
    // );

    try {
      await ref
          .read(genericRetailerProvider.notifier)
          .verifyRetailer(retailerId, action);

      // Refresh the retailer requests list to remove the processed request
      await ref.read(genericRetailerProvider.notifier).fetchRetailerRequests();

      // If retailer was approved, refresh the users list so they appear in Users tab
      if (action == 'approve') {
        await ref.read(usersProvider.notifier).fetchUsers();
      }

      if (!context.mounted) return;
      Navigator.pop(context);

      AppSnackbar.showSuccess(
        context,
        "Retailer ${action == 'approve' ? 'approved' : 'rejected'} successfully",
      );
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context);
        AppSnackbar.showError(context, 'Error: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final requestsAsync = ref.watch(usersProvider).newRequests;
    final retailerRequestsAsync =
        ref.watch(genericRetailerProvider).retailerRequests;

    // Handle error states
    if (requestsAsync is AsyncError) {
      final error = requestsAsync.error;
      final errorText = error.toString();
      final isNetworkError =
          error is SocketException ||
          errorText.toLowerCase().contains('failed host lookup') ||
          errorText.toLowerCase().contains('socketexception') ||
          errorText.toLowerCase().contains('network');

      if (isNetworkError) {
        return NetworkErrorWidget(
          message: 'Failed to load user requests',
          onRetry: () {
            ref.read(usersProvider.notifier).fetchNewUserRequests();
          },
        );
      }
      return Center(child: Text("Error loading user requests: $errorText"));
    }
    if (retailerRequestsAsync is AsyncError) {
      final error = retailerRequestsAsync.error;
      final errorText = error.toString();
      final isNetworkError =
          error is SocketException ||
          errorText.toLowerCase().contains('failed host lookup') ||
          errorText.toLowerCase().contains('socketexception') ||
          errorText.toLowerCase().contains('network');

      if (isNetworkError) {
        return NetworkErrorWidget(
          message: 'Failed to load retailer requests',
          onRetry: () {
            ref.read(genericRetailerProvider.notifier).fetchRetailerRequests();
          },
        );
      }
      return Center(child: Text("Error loading retailer requests: $errorText"));
    }

    // Get data or empty lists if still loading
    final requests = requestsAsync.asData?.value ?? [];
    final retailerRequests = retailerRequestsAsync.asData?.value ?? [];

    // Debug information (removed print statements for production)

    // Get all retailer request IDs to exclude them from user requests
    final retailerRequestIds = retailerRequests.map((r) => r['id']).toSet();

    // Filter out staff requests and retailer requests - they should only appear in their respective tabs
    final userRequests =
        requests.where((request) {
          // Exclude if it's a staff request
          if (request['account_type'] == 'staff' ||
              request['role'] == 'Staff' ||
              request['role'] == 'staff') {
            return false;
          }

          // Exclude if it's a retailer request (by account_type or role)
          if (request['account_type'] == 'retailer' ||
              request['role'] == 'Retailer' ||
              request['role'] == 'retailer') {
            return false;
          }

          // Exclude if this request ID is already in retailer requests
          if (retailerRequestIds.contains(request['id'])) {
            return false;
          }

          return true;
        }).toList();

    // Combine user requests and retailer requests
    final allRequests = <Map<String, dynamic>>[];

    // Add user requests with type indicator
    for (final request in userRequests) {
      allRequests.add({...request, 'request_type': 'user'});
    }

    // Add retailer requests with type indicator
    for (final request in retailerRequests) {
      allRequests.add({
        ...request,
        'request_type': 'retailer',
        'role': 'Retailer', // Ensure role is set for consistency
      });
    }

    if (allRequests.isEmpty) {
      return const Center(child: Text("No new requests found"));
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      itemCount: allRequests.length,
      itemBuilder: (context, index) {
        final request = allRequests[index];
        final isRetailerRequest = request['request_type'] == 'retailer';
        return Card(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 3,
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Request type indicator
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color:
                        isRetailerRequest
                            ? Colors.purple.withValues(alpha: 0.1)
                            : Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color:
                          isRetailerRequest
                              ? Colors.purple.withValues(alpha: 0.3)
                              : Colors.blue.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    isRetailerRequest ? 'Retailer Request' : 'User Request',
                    style: GoogleFonts.poppins(
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                      color:
                          isRetailerRequest
                              ? Colors.purple[700]
                              : Colors.blue[700],
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  request['name'] ?? '',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  request['email'] ?? '',
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  "Requested Role: ${request['role']}",
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    color: Colors.blueGrey,
                  ),
                ),
                // Show distributor info for retailer requests
                if (isRetailerRequest &&
                    request['distributor_name'] != null) ...[
                  const SizedBox(height: 6),
                  Text(
                    "Added by Distributor: ${request['distributor_name']}",
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: Colors.purple[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
                if (isRetailerRequest && request['company_name'] != null) ...[
                  const SizedBox(height: 6),
                  Text(
                    "Company: ${request['company_name']}",
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed:
                            () =>
                                isRetailerRequest
                                    ? _handleRetailerAction(
                                      context,
                                      ref,
                                      request['id'],
                                      'approve',
                                    )
                                    : _handleAction(
                                      context,
                                      ref,
                                      request['id'],
                                      'approve',
                                    ),
                        icon: const Icon(
                          Icons.check_circle_outline,
                          color: Colors.white,
                          size: 20,
                        ),
                        label: Text("Approve", style: GoogleFonts.poppins()),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green[600],
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed:
                            () =>
                                isRetailerRequest
                                    ? _handleRetailerAction(
                                      context,
                                      ref,
                                      request['id'],
                                      'reject',
                                    )
                                    : _handleAction(
                                      context,
                                      ref,
                                      request['id'],
                                      'reject',
                                    ),
                        icon: const Icon(
                          Icons.cancel_outlined,
                          color: Colors.white,
                          size: 20,
                        ),
                        label: Text("Reject", style: GoogleFonts.poppins()),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red[600],
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
