import 'dart:io';
import 'dart:ui';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/models/chat_user.dart';
import 'package:mr_garments_mobile/models/chat.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/models/group.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/services/optimized_image_service.dart';
import 'package:mr_garments_mobile/services/local_storage_service.dart';
import 'package:mr_garments_mobile/services/whatsapp_local_storage_service.dart';
import 'package:mr_garments_mobile/services/background_upload_service.dart'
    as bg;
import 'package:mr_garments_mobile/services/connectivity_service.dart';
import 'package:mr_garments_mobile/services/hive_chat_service.dart';
import 'package:mr_garments_mobile/models/local_message.dart';
import 'package:mr_garments_mobile/services/receiver_download_service.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_cache.dart';
import 'package:flutter/foundation.dart';

// ==================== CHAT STATE CLASSES ====================

class ChatState {
  final AsyncValue<List<Chat>> chats;
  final AsyncValue<List<ChatUser>> users;
  final AsyncValue<List<Group>> groups;
  final bool isLoading;
  final String? error;

  const ChatState({
    this.chats = const AsyncValue.loading(),
    this.users = const AsyncValue.loading(),
    this.groups = const AsyncValue.loading(),
    this.isLoading = false,
    this.error,
  });

  ChatState copyWith({
    AsyncValue<List<Chat>>? chats,
    AsyncValue<List<ChatUser>>? users,
    AsyncValue<List<Group>>? groups,
    bool? isLoading,
    String? error,
  }) {
    return ChatState(
      chats: chats ?? this.chats,
      users: users ?? this.users,
      groups: groups ?? this.groups,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

class MessageState {
  final AsyncValue<List<Message>> messages;
  final bool isLoading;
  final String? error;
  final Message? replyToMessage;
  final List<Message> pendingMessages;

  const MessageState({
    this.messages = const AsyncValue.loading(),
    this.isLoading = false,
    this.error,
    this.replyToMessage,
    this.pendingMessages = const [],
  });

  MessageState copyWith({
    AsyncValue<List<Message>>? messages,
    bool? isLoading,
    String? error,
    Message? replyToMessage,
    List<Message>? pendingMessages,
  }) {
    return MessageState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      replyToMessage: replyToMessage,
      pendingMessages: pendingMessages ?? this.pendingMessages,
    );
  }
}

// ==================== CHAT NOTIFIER ====================

class ChatNotifier extends StateNotifier<ChatState> {
  ChatNotifier() : super(const ChatState()) {
    _initializeChat();
  }

  Future<void> _initializeChat() async {
    try {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId != null) {
        await loadChats();
        await loadUsers();
        await loadGroups();
        await ChatService.initializeFCM();
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Load user's chats
  Future<void> loadChats() async {
    try {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) return;

      state = state.copyWith(isLoading: true);

      // This will be handled by the stream provider
      // Just update loading state here
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Load all users
  Future<void> loadUsers() async {
    try {
      state = state.copyWith(isLoading: true);
      final users = await ChatService.getAllUsers();
      state = state.copyWith(users: AsyncValue.data(users), isLoading: false);
    } catch (e) {
      state = state.copyWith(
        users: AsyncValue.error(e, StackTrace.current),
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Load user's groups
  Future<void> loadGroups() async {
    try {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) return;

      state = state.copyWith(isLoading: true);
      final groups = await ChatService.getUserGroups(currentUserId.toString());
      state = state.copyWith(groups: AsyncValue.data(groups), isLoading: false);
    } catch (e) {
      state = state.copyWith(
        groups: AsyncValue.error(e, StackTrace.current),
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Create individual chat
  Future<String?> createIndividualChat(String otherUserId) async {
    try {
      state = state.copyWith(isLoading: true);
      final chatId = await ChatService.createIndividualChat(otherUserId);
      state = state.copyWith(isLoading: false);
      await loadChats(); // Refresh chats
      return chatId;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return null;
    }
  }

  /// Create group
  Future<String?> createGroup({
    required String groupName,
    String? description,
    required List<String> memberIds,
    File? groupImage,
  }) async {
    try {
      state = state.copyWith(isLoading: true);
      final groupId = await ChatService.createGroup(
        groupName: groupName,
        description: description,
        memberIds: memberIds,
        groupImage: groupImage,
      );
      state = state.copyWith(isLoading: false);
      await loadChats(); // Refresh chats
      await loadGroups(); // Refresh groups
      return groupId;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return null;
    }
  }

  /// Search users
  Future<List<ChatUser>> searchUsers(String query) async {
    try {
      return await ChatService.searchUsers(query);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return [];
    }
  }

  /// Add member to group
  Future<bool> addMemberToGroup(String groupId, String userId) async {
    try {
      state = state.copyWith(isLoading: true);
      await ChatService.addMemberToGroup(groupId, userId);
      state = state.copyWith(isLoading: false);
      await loadGroups(); // Refresh groups
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Remove member from group
  Future<bool> removeMemberFromGroup(String groupId, String userId) async {
    try {
      state = state.copyWith(isLoading: true);
      await ChatService.removeMemberFromGroup(groupId, userId);
      state = state.copyWith(isLoading: false);
      await loadGroups(); // Refresh groups
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Clear chat messages
  Future<bool> clearChat(String chatId) async {
    try {
      state = state.copyWith(isLoading: true);
      await ChatService.clearChat(chatId);
      state = state.copyWith(isLoading: false);
      await loadChats(); // Refresh chats
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Delete chat
  Future<bool> deleteChat(String chatId) async {
    try {
      state = state.copyWith(isLoading: true);
      await ChatService.deleteChat(chatId);
      state = state.copyWith(isLoading: false);
      await loadChats(); // Refresh chats
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// ==================== MESSAGE NOTIFIER ====================

class MessageNotifier extends StateNotifier<MessageState> {
  final String chatId;
  
  // Static set to track downloads in progress across all chat instances
  static final Set<String> _downloadsInProgress = {};

  MessageNotifier(this.chatId) : super(const MessageState());

  String _getMessageDisplayText(Message message) {
    // First check if we have text content regardless of type
    if (message.text != null && message.text!.isNotEmpty) {
      return message.text!;
    }

    // Then handle specific media types
    switch (message.type) {
      case MessageType.image:
        return '📷 Image';
      case MessageType.file:
        return '📎 ${message.fileName ?? 'File'}';
      case MessageType.audio:
        return '🎵 Audio';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.location:
        return '📍 Location';
      case MessageType.contact:
        return '👤 Contact';
      case MessageType.catalog:
        return '📋 Catalog';
      case MessageType.text:
      default:
        // Fallback - this should rarely be reached now
        return message.text ?? 'Message';
    }
  }

  /// Send text message
  Future<bool> sendTextMessage(String text, {String? replyToMessageId}) async {
    try {
      state = state.copyWith(isLoading: true);

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      await ChatService.sendTextMessage(
        chatId: chatId,
        text: text,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
      );
      state = state.copyWith(isLoading: false, replyToMessage: null);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Send image message
  Future<bool> sendImageMessage(
    File imageFile, {
    String? replyToMessageId,
  }) async {
    try {
      state = state.copyWith(isLoading: true);

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      await ChatService.sendImageMessage(
        chatId: chatId,
        imageFile: imageFile,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
      );
      state = state.copyWith(isLoading: false, replyToMessage: null);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Send multiple images with WhatsApp-like immediate display
  Future<bool> sendMultipleImagesWithInstantDisplay(
    List<ProcessedImage> images, {
    String? replyToMessageId,
  }) async {
    if (images.isEmpty) return false;

    try {
      // Get current user info
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      // Create pending messages immediately for instant display
      final pendingMessages = <Message>[];
      final baseTime = DateTime.now();

      for (int i = 0; i < images.length; i++) {
        final image = images[i];
        final messageId =
            'pending_${DateTime.now().millisecondsSinceEpoch}${currentUserId}$i';

        final pendingMessage = Message(
          id: messageId,
          senderId: currentUserId.toString(),
          senderName: currentUserName,
          type: MessageType.image,
          mediaUrl:
              image
                  .compressedFile
                  .path, // Use local file path for immediate display
          timestamp: baseTime.add(Duration(milliseconds: i)),
          replyToMessageId:
              i == 0 ? replyToMessageId : null, // Only first image has reply
          replyToText: i == 0 ? replyToText : null,
          replyToSenderName: i == 0 ? replyToSenderName : null,
          status: MessageStatus.sending,
          metadata: {
            'isLocalFile': true, // Flag to indicate this is a local file
            'batchIndex': i,
            'batchTotal': images.length,
            'originalPath': image.originalPath,
            'fileSize': image.fileSize,
            'pendingMessageId':
                messageId, // Add self-reference for easier cleanup
          },
        );

        pendingMessages.add(pendingMessage);
      }

      // Add pending messages to local state immediately
      await _addPendingMessagesToLocalState(pendingMessages);

      // Clear reply state
      state = state.copyWith(replyToMessage: null);

      // Send images in background
      _sendImagesInBackground(
        images,
        pendingMessages,
        replyToMessageId,
        replyToText,
        replyToSenderName,
      );

      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// WhatsApp-like instant image display with background upload
  Future<bool> sendOptimizedImages(
    List<ProcessedImage> images, {
    String? replyToMessageId,
  }) async {
    if (images.isEmpty) return false;

    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        state = state.copyWith(error: 'User not logged in');
        return false;
      }

      final replyToMessage = state.replyToMessage;
      final replyToText = replyToMessage?.text;
      final replyToSenderName = replyToMessage?.senderName;

      // Create optimistic messages that appear INSTANTLY in chat
      final List<Message> optimisticMessages = [];
      final baseTime = DateTime.now();

      for (int i = 0; i < images.length; i++) {
        final image = images[i];
        final messageId =
            'instant_${DateTime.now().millisecondsSinceEpoch}${currentUserId}$i';

        final optimisticMessage = Message(
          id: messageId,
          senderId: currentUserId.toString(),
          senderName: currentUserName,
          type: MessageType.image,
          mediaUrl: image.compressedFile.path, // Local file for instant display
          timestamp: baseTime.add(Duration(milliseconds: i)),
          replyToMessageId: i == 0 ? replyToMessageId : null,
          replyToText: i == 0 ? replyToText : null,
          replyToSenderName: i == 0 ? replyToSenderName : null,
          status: MessageStatus.sending, // Shows clock icon like WhatsApp
          metadata: {
            'isLocalFile': true, // Flag for local display
            'batchIndex': i,
            'batchTotal': images.length,
            'originalPath': image.originalPath,
            'fileSize': image.fileSize,
            'isOptimistic': true, // Mark as optimistic message
          },
        );

        optimisticMessages.add(optimisticMessage);
      }

      // INSTANT DISPLAY: Add all images to chat immediately
      final currentPending = state.pendingMessages;
      final updatedPending = [...currentPending, ...optimisticMessages];
      state = state.copyWith(
        pendingMessages: updatedPending,
        replyToMessage: null,
      );

      // BACKGROUND PROCESSING: Upload one by one without blocking UI
      _sendImagesOneByOneInBackground(
        images,
        optimisticMessages,
        replyToMessageId,
        replyToText,
        replyToSenderName,
      );

      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Enhanced WhatsApp-like image sending with local storage
  Future<bool> sendImagesWithLocalStorage(
    List<ProcessedImage> images, {
    String? replyToMessageId,
  }) async {
    if (images.isEmpty) return false;

    try {
      // Get current user info
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      final baseTime = DateTime.now();
      final batchId = 'batch_${baseTime.millisecondsSinceEpoch}';
      final List<Message> optimisticMessages = [];

      for (int i = 0; i < images.length; i++) {
        final image = images[i];
        final messageId = 'local_${baseTime.millisecondsSinceEpoch}_$i';

        // Store image locally first
        final localResult = await LocalStorageService.storeImageLocally(
          sourceFile: image.compressedFile,
          messageId: messageId,
          chatId: chatId,
        );

        if (!localResult.success) {
          throw Exception(
            'Failed to store image locally: ${localResult.error}',
          );
        }

        // Create optimistic message with local paths
        final optimisticMessage = Message(
          id: messageId,
          senderId: currentUserId.toString(),
          senderName: currentUserName,
          type: MessageType.image,
          localImagePath: localResult.localImagePath,
          localThumbnailPath: localResult.thumbnailPath,
          timestamp: baseTime.add(Duration(milliseconds: i)),
          replyToMessageId: i == 0 ? replyToMessageId : null,
          replyToText: i == 0 ? replyToText : null,
          replyToSenderName: i == 0 ? replyToSenderName : null,
          optimisticStatus: OptimisticMessageStatus.pending,
          uploadStatus: UploadStatus.pending,
          isLocalOnly: true,
          metadata: {
            'batchId': batchId,
            'batchIndex': i,
            'batchTotal': images.length,
            'originalPath': image.originalPath,
            'fileSize': localResult.imageSize,
            'thumbnailSize': localResult.thumbnailSize,
          },
        );

        optimisticMessages.add(optimisticMessage);

        // Queue for background upload
        bg.BackgroundUploadService.instance.queueUpload(
          messageId: messageId,
          chatId: chatId,
          localImagePath: localResult.localImagePath!,
          thumbnailPath: localResult.thumbnailPath,
          replyToMessageId: i == 0 ? replyToMessageId : null,
          replyToText: i == 0 ? replyToText : null,
          replyToSenderName: i == 0 ? replyToSenderName : null,
          metadata: optimisticMessage.metadata,
          onProgress:
              (progress) => _updateMessageUploadProgress(messageId, progress),
          onSuccess:
              (uploadUrl) => _updateMessageUploadSuccess(messageId, uploadUrl),
          onError: (error) => _updateMessageUploadError(messageId, error),
        );
      }

      // Add optimistic messages to state immediately
      await _addOptimisticMessagesToState(optimisticMessages);

      // Clear reply state
      state = state.copyWith(replyToMessage: null);

      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// WhatsApp-like image sending with external storage (android/media/MrGarments/sender)
  Future<bool> sendImagesWithWhatsAppStorage(
    List<ProcessedImage> images, {
    String? replyToMessageId,
  }) async {
    if (images.isEmpty) return false;

    try {
      // Initialize WhatsApp local storage
      await WhatsAppLocalStorageService.initialize();

      // Get current user info
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      final baseTime = DateTime.now();
      final batchId = 'whatsapp_batch_${baseTime.millisecondsSinceEpoch}';
      final List<Message> optimisticMessages = [];

      for (int i = 0; i < images.length; i++) {
        final image = images[i];
        final messageId = 'whatsapp_${baseTime.millisecondsSinceEpoch}_$i';

        // Store image in WhatsApp-like external storage as sender
        final senderResult =
            await WhatsAppLocalStorageService.storeImageAsSender(
              sourceFile: image.compressedFile,
              messageId: messageId,
              chatId: chatId,
            );

        if (!senderResult.success) {
          throw Exception(
            'Failed to store image in sender storage: ${senderResult.error}',
          );
        }

        // Create optimistic message with WhatsApp local paths
        final optimisticMessage = Message(
          id: messageId,
          senderId: currentUserId.toString(),
          senderName: currentUserName,
          type: MessageType.image,
          localImagePath: senderResult.localImagePath,
          localThumbnailPath: senderResult.thumbnailPath,
          timestamp: baseTime.add(Duration(milliseconds: i)),
          replyToMessageId: i == 0 ? replyToMessageId : null,
          replyToText: i == 0 ? replyToText : null,
          replyToSenderName: i == 0 ? replyToSenderName : null,
          optimisticStatus: OptimisticMessageStatus.pending,
          uploadStatus: UploadStatus.pending,
          isLocalOnly: true,
          metadata: {
            'batchId': batchId,
            'batchIndex': i,
            'batchTotal': images.length,
            'originalPath': image.originalPath,
            'fileSize': senderResult.imageSize,
            'thumbnailSize': senderResult.thumbnailSize,
            'encryptedFileName': senderResult.encryptedFileName,
            'whatsappStorage': true, // Flag to identify WhatsApp storage
          },
        );

        optimisticMessages.add(optimisticMessage);

        // Queue for background upload with WhatsApp storage cleanup
        bg.BackgroundUploadService.instance.queueUpload(
          messageId: messageId,
          chatId: chatId,
          localImagePath: senderResult.localImagePath!,
          thumbnailPath: senderResult.thumbnailPath,
          replyToMessageId: i == 0 ? replyToMessageId : null,
          replyToText: i == 0 ? replyToText : null,
          replyToSenderName: i == 0 ? replyToSenderName : null,
          metadata: optimisticMessage.metadata,
          onProgress:
              (progress) => _updateMessageUploadProgress(messageId, progress),
          onSuccess:
              (uploadUrl) => _updateWhatsAppMessageUploadSuccess(
                messageId,
                uploadUrl,
                senderResult.encryptedFileName!,
              ),
          onError: (error) => _updateMessageUploadError(messageId, error),
        );
      }

      // Add optimistic messages to state immediately
      await _addOptimisticMessagesToState(optimisticMessages);

      // Clear reply state
      state = state.copyWith(replyToMessage: null);

      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Add pending messages to local state for immediate display
  Future<void> _addPendingMessagesToLocalState(
    List<Message> pendingMessages,
  ) async {
    // Add pending messages to the current state
    final currentPending = state.pendingMessages;
    final updatedPending = [...currentPending, ...pendingMessages];
    state = state.copyWith(pendingMessages: updatedPending);
  }

  /// Add optimistic messages to state for immediate display
  Future<void> _addOptimisticMessagesToState(
    List<Message> optimisticMessages,
  ) async {
    // Add optimistic messages to the current state
    final currentPending = state.pendingMessages;
    final updatedPending = [...currentPending, ...optimisticMessages];
    state = state.copyWith(pendingMessages: updatedPending);
  }

  /// Update message upload progress
  void _updateMessageUploadProgress(
    String messageId,
    bg.UploadProgress progress,
  ) {
    final currentPending = state.pendingMessages;
    final updatedPending =
        currentPending.map((message) {
          if (message.id == messageId) {
            return message.copyWith(
              uploadProgress: progress.progress,
              uploadStatus: _convertUploadStatus(progress.status),
              optimisticStatus:
                  progress.status == bg.UploadStatus.uploading
                      ? OptimisticMessageStatus.uploading
                      : OptimisticMessageStatus.pending,
            );
          }
          return message;
        }).toList();

    state = state.copyWith(pendingMessages: updatedPending);
  }

  /// Update message upload success - just update the optimistic message
  void _updateMessageUploadSuccess(String messageId, String uploadUrl) async {
    final currentPending = state.pendingMessages;
    final updatedPending =
        currentPending.map((message) {
          if (message.id == messageId) {
            return message.copyWith(
              mediaUrl: uploadUrl,
              uploadStatus: UploadStatus.completed,
              optimisticStatus: OptimisticMessageStatus.sent,
              uploadProgress: 1.0,
              isLocalOnly: false,
            );
          }
          return message;
        }).toList();

    state = state.copyWith(pendingMessages: updatedPending);

    // Create Firebase message in background without blocking UI
    _createFirebaseMessageInBackground(messageId, uploadUrl);
  }

  /// Update WhatsApp message upload success with receiver storage and Firebase cleanup
  void _updateWhatsAppMessageUploadSuccess(
    String messageId,
    String uploadUrl,
    String encryptedFileName,
  ) async {
    final currentPending = state.pendingMessages;
    final updatedPending =
        currentPending.map((message) {
          if (message.id == messageId) {
            return message.copyWith(
              mediaUrl: uploadUrl,
              uploadStatus: UploadStatus.completed,
              optimisticStatus: OptimisticMessageStatus.sent,
              uploadProgress: 1.0,
              isLocalOnly: false,
            );
          }
          return message;
        }).toList();

    state = state.copyWith(pendingMessages: updatedPending);

    // Create Firebase message and handle WhatsApp storage flow
    _createWhatsAppFirebaseMessageInBackground(
      messageId,
      uploadUrl,
      encryptedFileName,
    );
  }

  /// Create Firebase message in background with retry logic
  void _createFirebaseMessageInBackground(
    String messageId,
    String uploadUrl, {
    int retryCount = 0,
    int maxRetries = 5,
  }) async {
    try {
      // Find the optimistic message
      final currentPending = state.pendingMessages;
      final optimisticMessage = currentPending.firstWhere(
        (message) => message.id == messageId,
        orElse: () => throw Exception('Optimistic message not found'),
      );

      // Create real Firebase message
      await ChatService.sendImageMessage(
        chatId: chatId,
        imageFile: File(optimisticMessage.localImagePath!),
        replyToMessageId: optimisticMessage.replyToMessageId,
        replyToText: optimisticMessage.replyToText,
        replyToSenderName: optimisticMessage.replyToSenderName,
        extraMetadata: {
          ...?optimisticMessage.metadata,
          'localImagePath': optimisticMessage.localImagePath,
          'thumbnailPath': optimisticMessage.localThumbnailPath,
          'uploadUrl': uploadUrl,
          'uploadedAt': DateTime.now().toIso8601String(),
          'optimisticMessageId': messageId, // Link to optimistic message
        },
      );

      debugPrint('Firebase message created for optimistic message: $messageId');
    } catch (e) {
      debugPrint('❌ Error creating Firebase message (attempt ${retryCount + 1}/$maxRetries): $e',
      );

      // Retry logic with exponential backoff
      if (retryCount < maxRetries) {
        final delaySeconds = (retryCount + 1) * 3; // 3s, 6s, 9s, 12s, 15s
        debugPrint(
          '🔄 Retrying Firebase message creation in ${delaySeconds}s...',
        );

        await Future.delayed(Duration(seconds: delaySeconds));

        // Retry
        _createFirebaseMessageInBackground(
          messageId,
          uploadUrl,
          retryCount: retryCount + 1,
          maxRetries: maxRetries,
        );
      } else {
        debugPrint(
          '⚠ Max retries reached for Firebase message creation: $messageId',
        );
        // Mark message as failed to create in Firebase
        _updateMessageUploadError(
          messageId,
          'Failed to create Firebase message after $maxRetries retries',
        );
      }
    }
  }

  /// Create WhatsApp Firebase message and handle receiver storage + cleanup with retry logic
  void _createWhatsAppFirebaseMessageInBackground(
    String messageId,
    String uploadUrl,
    String encryptedFileName, {
    int retryCount = 0,
    int maxRetries = 5,
  }) async {
    try {
      // Find the optimistic message
      final currentPending = state.pendingMessages;
      final optimisticMessage = currentPending.firstWhere(
        (message) => message.id == messageId,
        orElse: () => throw Exception('Optimistic message not found'),
      );

      // Create real Firebase message with WhatsApp metadata
      await ChatService.sendImageMessage(
        chatId: chatId,
        imageFile: File(optimisticMessage.localImagePath!),
        replyToMessageId: optimisticMessage.replyToMessageId,
        replyToText: optimisticMessage.replyToText,
        replyToSenderName: optimisticMessage.replyToSenderName,
        extraMetadata: {
          ...?optimisticMessage.metadata,
          'localImagePath': optimisticMessage.localImagePath,
          'thumbnailPath': optimisticMessage.localThumbnailPath,
          'uploadUrl': uploadUrl,
          'uploadedAt': DateTime.now().toIso8601String(),
          'optimisticMessageId': messageId,
          'whatsappStorage': true,
          'encryptedFileName': encryptedFileName,
          'senderStoragePath': optimisticMessage.localImagePath,
        },
      );

      debugPrint('✅ WhatsApp Firebase message created: $messageId');
      debugPrint('📤 Sender storage: ${optimisticMessage.localImagePath}');
      debugPrint('🔗 Firebase URL: $uploadUrl');
    } catch (e) {
      debugPrint(
        '❌ Error creating WhatsApp Firebase message (attempt ${retryCount + 1}/$maxRetries): $e',
      );

      // Retry logic with exponential backoff
      if (retryCount < maxRetries) {
        final delaySeconds = (retryCount + 1) * 3; // 3s, 6s, 9s, 12s, 15s
        debugPrint(
          '🔄 Retrying WhatsApp Firebase message creation in ${delaySeconds}s...',
        );

        await Future.delayed(Duration(seconds: delaySeconds));

        // Retry
        _createWhatsAppFirebaseMessageInBackground(
          messageId,
          uploadUrl,
          encryptedFileName,
          retryCount: retryCount + 1,
          maxRetries: maxRetries,
        );
      } else {
        debugPrint(
          '⚠ Max retries reached for WhatsApp Firebase message creation: $messageId',
        );
        // Mark message as failed to create in Firebase
        _updateMessageUploadError(
          messageId,
          'Failed to create WhatsApp Firebase message after $maxRetries retries',
        );
      }
    }
  }

  /// Update message upload error
  void _updateMessageUploadError(String messageId, String error) {
    final currentPending = state.pendingMessages;
    final updatedPending =
        currentPending.map((message) {
          if (message.id == messageId) {
            return message.copyWith(
              uploadStatus: UploadStatus.failed,
              optimisticStatus: OptimisticMessageStatus.failed,
              uploadProgress: 0.0,
            );
          }
          return message;
        }).toList();

    state = state.copyWith(pendingMessages: updatedPending);
  }

  /// Convert background service upload status to message upload status
  UploadStatus _convertUploadStatus(bg.UploadStatus bgStatus) {
    switch (bgStatus) {
      case bg.UploadStatus.pending:
        return UploadStatus.pending;
      case bg.UploadStatus.uploading:
        return UploadStatus.uploading;
      case bg.UploadStatus.completed:
        return UploadStatus.completed;
      case bg.UploadStatus.failed:
        return UploadStatus.failed;
      case bg.UploadStatus.cancelled:
        return UploadStatus.cancelled;
    }
  }

  /// WhatsApp-like: Send images one by one in background while showing all instantly
  void _sendImagesOneByOneInBackground(
    List<ProcessedImage> images,
    List<Message> optimisticMessages,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  ) async {
    final baseTime = DateTime.now();

    // Send images ONE BY ONE like WhatsApp (not in batches)
    for (int i = 0; i < images.length; i++) {
      final image = images[i];
      final optimisticMessage = optimisticMessages[i];

      try {
        // Update status to uploading (single tick)
        await _updateOptimisticMessageStatus(
          optimisticMessage.id,
          MessageStatus.sending,
        );

        // Send message to Firebase (includes upload and message creation)
        await ChatService.sendImageMessage(
          chatId: chatId,
          imageFile: image.compressedFile,
          replyToMessageId: i == 0 ? replyToMessageId : null,
          replyToText: i == 0 ? replyToText : null,
          replyToSenderName: i == 0 ? replyToSenderName : null,
          timestampOverride: baseTime.add(Duration(milliseconds: i)),
          extraMetadata: {
            'batchIndex': i,
            'batchTotal': images.length,
            'optimisticMessageId': optimisticMessage.id,
          },
        );

        // Update status to sent (double tick)
        await _updateOptimisticMessageStatus(
          optimisticMessage.id,
          MessageStatus.sent,
        );

        // Small delay between images like WhatsApp
        await Future.delayed(const Duration(milliseconds: 100));
      } catch (e) {
        // Update status to failed (red exclamation)
        await _updateOptimisticMessageStatus(
          optimisticMessage.id,
          MessageStatus.failed,
        );
      }
    }
  }

  /// Send images in background with concurrent processing for maximum speed
  void _sendImagesInBackground(
    List<ProcessedImage> images,
    List<Message> pendingMessages,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  ) async {
    final baseTime = DateTime.now();
    const batchSize = 12; // Process 12 images concurrently

    // Process images in batches for optimal performance
    for (int i = 0; i < images.length; i += batchSize) {
      final batch = images.skip(i).take(batchSize).toList();
      final batchMessages = pendingMessages.skip(i).take(batchSize).toList();

      // Send batch concurrently
      final futures = batch.asMap().entries.map((entry) {
        final index = i + entry.key;
        final image = entry.value;
        final pendingMessage = batchMessages[entry.key];

        return _sendSingleImageWithRetry(
          image: image,
          pendingMessage: pendingMessage,
          index: index,
          baseTime: baseTime,
          replyToMessageId: index == 0 ? replyToMessageId : null,
          replyToText: index == 0 ? replyToText : null,
          replyToSenderName: index == 0 ? replyToSenderName : null,
          totalImages: images.length,
        );
      });

      // Wait for current batch to complete before starting next batch
      await Future.wait(futures);
    }
  }

  /// Send single image with retry logic
  Future<void> _sendSingleImageWithRetry({
    required ProcessedImage image,
    required Message pendingMessage,
    required int index,
    required DateTime baseTime,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    required int totalImages,
  }) async {
    try {
      // Send individual image
      await ChatService.sendImageMessage(
        chatId: chatId,
        imageFile: image.compressedFile,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        timestampOverride: baseTime.add(Duration(milliseconds: index)),
        extraMetadata: {
          'batchIndex': index,
          'batchTotal': totalImages,
          'pendingMessageId': pendingMessage.id, // Link to pending message
        },
      );

      // Update pending message status to sent
      await _updatePendingMessageStatus(pendingMessage.id, MessageStatus.sent);
    } catch (e) {
      // Update pending message status to failed
      await _updatePendingMessageStatus(
        pendingMessage.id,
        MessageStatus.failed,
      );
    }
  }

  /// Update optimistic message status (for WhatsApp-like instant display)
  Future<void> _updateOptimisticMessageStatus(
    String messageId,
    MessageStatus status,
  ) async {
    final currentPending = state.pendingMessages;
    final updatedPending =
        currentPending.map((message) {
          if (message.id == messageId) {
            return message.copyWith(status: status);
          }
          return message;
        }).toList();

    state = state.copyWith(pendingMessages: updatedPending);
  }

  /// Update pending message status
  Future<void> _updatePendingMessageStatus(
    String pendingMessageId,
    MessageStatus status,
  ) async {
    final currentPending = state.pendingMessages;
    final updatedPending =
        currentPending.map((message) {
          if (message.id == pendingMessageId) {
            return message.copyWith(status: status);
          }
          return message;
        }).toList();

    state = state.copyWith(pendingMessages: updatedPending);

    // Don't remove pending messages immediately - let them be replaced by real messages
    // The combination logic in _buildMessagesList will handle the replacement
  }

  /// Remove a pending message (when real message appears or after timeout)
  void _removePendingMessage(String pendingMessageId) {
    final currentPending = state.pendingMessages;
    final updatedPending =
        currentPending
            .where((message) => message.id != pendingMessageId)
            .toList();
    state = state.copyWith(pendingMessages: updatedPending);
  }

  /// Clean up old pending messages (called periodically to prevent memory leaks)
  void _cleanupOldPendingMessages() {
    final now = DateTime.now();
    final currentPending = state.pendingMessages;

    // Remove pending messages older than 5 minutes
    final updatedPending =
        currentPending.where((message) {
          final age = now.difference(message.timestamp);
          return age.inMinutes < 5;
        }).toList();

    if (updatedPending.length != currentPending.length) {
      state = state.copyWith(pendingMessages: updatedPending);
    }
  }

  /// Send file message
  Future<bool> sendFileMessage(File file, {String? replyToMessageId}) async {
    try {
      state = state.copyWith(isLoading: true);

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      await ChatService.sendFileMessage(
        chatId: chatId,
        file: file,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
      );
      state = state.copyWith(isLoading: false, replyToMessage: null);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Send single catalog message
  Future<bool> sendSingleCatalogMessage(
    Map<String, dynamic> catalog, {
    String? replyToMessageId,
  }) async {
    try {
      state = state.copyWith(isLoading: true);

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      await ChatService.sendSingleCatalogMessage(
        chatId: chatId,
        catalog: catalog,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
      );
      state = state.copyWith(isLoading: false, replyToMessage: null);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Send catalog message (multiple catalogs - kept for backward compatibility)
  Future<bool> sendCatalogMessage(
    List<Map<String, dynamic>> catalogs, {
    String? replyToMessageId,
  }) async {
    try {
      state = state.copyWith(isLoading: true);

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      await ChatService.sendCatalogMessage(
        chatId: chatId,
        catalogs: catalogs,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
      );
      state = state.copyWith(isLoading: false, replyToMessage: null);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Set reply to message
  void setReplyToMessage(Message? message) {
    state = state.copyWith(replyToMessage: message);
  }

  /// Clear reply
  void clearReply() {
    state = state.copyWith(replyToMessage: null);
  }

  /// Preload images from messages for better performance with enhanced caching
  Future<void> preloadImagesFromMessages(List<Message> messages) async {
    final imageUrls =
        messages
            .where(
              (message) =>
                  message.type == MessageType.image &&
                  message.mediaUrl != null &&
                  message.mediaUrl!.isNotEmpty,
            )
            .map((message) => message.mediaUrl!)
            .toList();

    if (imageUrls.isNotEmpty) {
      // Preload images in background with enhanced caching
      EnhancedImageCache.preloadImages(
        imageUrls,
        onProgress: (loaded, total) {
          // Optional: Could emit progress updates if needed
          // print('Preloaded $loaded/$total images for chat $chatId');
        },
        onComplete: () {
          // print('All images preloaded for chat $chatId');
        },
      );
    }
  }

  /// Preload images immediately when messages are loaded
  void _preloadImagesFromNewMessages(List<Message> messages) {
    // Run preloading in background without awaiting
    Future.microtask(() => preloadImagesFromMessages(messages));
  }

  /// Mark message as read
  Future<void> markMessageAsRead(String messageId) async {
    try {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId != null) {
        await ChatService.markMessageAsRead(
          chatId,
          messageId,
          currentUserId.toString(),
        );
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Mark all messages as read
  Future<void> markAllMessagesAsRead(String userId) async {
    try {
      await ChatService.markAllMessagesAsRead(chatId, userId);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Delete message
  Future<bool> deleteMessage(String messageId) async {
    try {
      await ChatService.deleteMessage(chatId, messageId);
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Forward message
  Future<bool> forwardMessage(String messageId, List<String> toChatIds) async {
    try {
      state = state.copyWith(isLoading: true);
      await ChatService.forwardMessage(
        fromChatId: chatId,
        messageId: messageId,
        toChatIds: toChatIds,
      );
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Handle WhatsApp receiver storage for incoming image messages
  /// Auto-downloads images to local storage, user clicks download to make them visible
  void _handleWhatsAppReceiverStorage(
    List<Message> messages,
    String? currentUserId,
  ) {
    if (currentUserId == null) return;

    for (final message in messages) {
      // Only process image messages that are not from current user and use WhatsApp storage
      if (message.type == MessageType.image &&
          message.senderId != currentUserId &&
          ChatService.isWhatsAppStorageMessage(message) &&
          message.mediaUrl != null) {
        // Check if image is already downloaded or being processed
        if (message.metadata?['autoDownloaded'] == true ||
            message.metadata?['autoDownloading'] == true) {
          continue;
        }
        
        // Check if download is already in progress (in-memory check to prevent race conditions)
        if (_downloadsInProgress.contains(message.id)) {
          debugPrint('⏳ Download already in progress for message: ${message.id}');
          continue;
        }

        debugPrint('📱 Auto-downloading image to local storage: ${message.id}');

        // Auto-download image to local storage in background
        _autoDownloadImageToLocalStorage(message, currentUserId);
      }
    }
  }

  /// Auto-download image to local storage without showing in chat
  Future<void> _autoDownloadImageToLocalStorage(
    Message message,
    String currentUserId,
  ) async {
    // Add to in-progress set immediately
    _downloadsInProgress.add(message.id);
    
    try {
      // Mark as downloading to prevent duplicate downloads
      await ChatService.updateMessageMetadata(
        chatId: chatId,
        messageId: message.id,
        metadata: {'autoDownloading': true},
      );

      debugPrint('🔄 Starting auto-download for message: ${message.id}');

      // Download image to receiver folder
      final success = await ReceiverDownloadService.downloadImageForReceiver(
        messageId: message.id,
        chatId: chatId,
        downloadUrl: message.mediaUrl!,
        senderId: message.senderId,
        onProgress: (progress) {
          // Silent download, no UI updates
        },
      );

      if (success) {
        // Mark as auto-downloaded but not visible in chat yet
        await ChatService.updateMessageMetadata(
          chatId: chatId,
          messageId: message.id,
          metadata: {
            'autoDownloaded': true,
            'autoDownloading': false,
            'readyForDisplay': true, // User needs to click download to show
          },
        );

        debugPrint('✅ Auto-download completed for message: ${message.id}');

        // Schedule Firebase cleanup after successful download
        _scheduleFirebaseCleanup(message.mediaUrl!, message.id);
      } else {
        // Remove downloading flag on failure
        await ChatService.updateMessageMetadata(
          chatId: chatId,
          messageId: message.id,
          metadata: {'autoDownloading': false},
        );
        debugPrint('❌ Auto-download failed for message: ${message.id}');
      }
    } catch (e) {
      debugPrint('❌ Error in auto-download: $e');
      // Remove downloading flag on error
      await ChatService.updateMessageMetadata(
        chatId: chatId,
        messageId: message.id,
        metadata: {'autoDownloading': false},
      );
    } finally {
      // Always remove from in-progress set when done
      _downloadsInProgress.remove(message.id);
    }
  }

  /// Schedule Firebase cleanup after successful local storage
  void _scheduleFirebaseCleanup(String downloadUrl, String messageId) {
    // Schedule cleanup after a short delay to ensure download is complete
    Future.delayed(const Duration(minutes: 1), () async {
      try {
        await ChatService.deleteFirebaseImage(downloadUrl);
        debugPrint('🗑 Firebase image cleaned up for message: $messageId');
      } catch (e) {
        debugPrint('❌ Failed to cleanup Firebase image: $e');
      }
    });
  }

  /// Clear all pending messages for this chat (used when clearing/deleting chat)
  void clearPendingMessages() {
    state = state.copyWith(pendingMessages: []);
    debugPrint('🧹 Cleared pending messages for chat: $chatId');
  }

  /// Clear error state
}

// ==================== PROVIDERS ====================

/// Main chat provider
final chatProvider = StateNotifierProvider<ChatNotifier, ChatState>(
  (ref) => ChatNotifier(),
);

/// Message provider for specific chat
final messageProvider =
    StateNotifierProvider.family<MessageNotifier, MessageState, String>(
      (ref, chatId) => MessageNotifier(chatId),
    );

// ==================== SESSION PROVIDERS ====================

/// Provider for current user ID that refreshes when user changes
final currentUserIdProvider = StreamProvider<String?>((ref) async* {
  String? lastUserId;

  while (true) {
    final currentUserId = await SessionService.getUserId();
    final currentUserIdStr = currentUserId?.toString();

    if (currentUserIdStr != lastUserId) {
      lastUserId = currentUserIdStr;
      yield currentUserIdStr;
    }

    // Check every 1 second for user changes
    await Future.delayed(const Duration(seconds: 1));
  }
});

/// Stream provider for user's chats (all chats)
final userChatsStreamProvider = StreamProvider<List<Chat>>((ref) async* {
  final currentUserId = await SessionService.getUserId();
  if (currentUserId != null) {
    yield* ChatService.getUserChatsStream(currentUserId.toString());
  } else {
    yield [];
  }
});

/// Stream provider for user's individual chats only
final individualChatsStreamProvider = StreamProvider.autoDispose<List<Chat>>((
  ref,
) async* {
  // Get current user ID and refresh when it changes
  final currentUserId = await SessionService.getUserId();

  if (currentUserId != null) {
    yield* ChatService.getUserChatsStream(currentUserId.toString()).map(
      (chats) =>
          chats.where((chat) => chat.type == ChatType.individual).toList(),
    );
  } else {
    yield [];
  }
});

/// Stream provider for user's group chats only
final groupChatsStreamProvider = StreamProvider<List<Chat>>((ref) async* {
  final currentUserId = await SessionService.getUserId();
  if (currentUserId != null) {
    yield* ChatService.getUserChatsStream(currentUserId.toString()).map(
      (chats) => chats.where((chat) => chat.type == ChatType.group).toList(),
    );
  } else {
    yield [];
  }
});

/// Enhanced stream provider for messages with automatic image preloading and user-specific filtering
final messagesStreamProvider = StreamProvider.family
    .autoDispose<List<Message>, String>((ref, chatId) async* {
      // Get current user ID to filter deleted messages
      final currentUserId = await SessionService.getUserId();
      final currentUserIdString = currentUserId?.toString();

      await for (final messages in ChatService.getMessagesStream(chatId)) {
        // Filter out messages that are deleted by the current user
        final filteredMessages =
            currentUserIdString != null
                ? messages
                    .where(
                      (message) => !message.isDeletedBy(currentUserIdString),
                    )
                    .toList()
                : messages;

        // Preload images from new messages in background
        final messageNotifier = ref.read(messageProvider(chatId).notifier);
        messageNotifier._preloadImagesFromNewMessages(filteredMessages);

        // Handle WhatsApp receiver storage for new image messages
        messageNotifier._handleWhatsAppReceiverStorage(
          filteredMessages,
          currentUserIdString,
        );

        yield filteredMessages;
      }
    });

/// Provider for current user
final currentUserProvider = FutureProvider<ChatUser?>((ref) async {
  final currentUserId = await SessionService.getUserId();
  if (currentUserId != null) {
    return await ChatService.getUser(currentUserId.toString());
  }
  return null;
});

/// Provider for other user (for online status) - Real-time stream
final otherUserProvider = StreamProvider.family.autoDispose<ChatUser?, String>((
  ref,
  userId,
) {
  return ChatService.getUserStream(userId);
});

/// Provider for specific chat
final chatDetailProvider = FutureProvider.family<Chat?, String>((
  ref,
  chatId,
) async {
  return await ChatService.getChat(chatId);
});

/// Provider for specific group
final groupProvider = FutureProvider.family<Group?, String>((
  ref,
  groupId,
) async {
  return await ChatService.getGroup(groupId);
});

/// Provider for all users (for member selection)
final allUsersProvider = FutureProvider<List<ChatUser>>((ref) async {
  return await ChatService.getAllUsers();
});

/// Provider for user search results
final userSearchProvider = StateProvider<List<ChatUser>>((ref) => []);

/// Provider for selected users (for group creation)
final selectedUsersProvider = StateProvider<List<ChatUser>>((ref) => []);

/// Provider for selected messages (for forwarding/deleting)
final selectedMessagesProvider = StateProvider<List<Message>>((ref) => []);

/// Provider for pending messages (local messages being sent)
final pendingMessagesProvider = StateProvider.family<List<Message>, String>(
  (ref, chatId) => [],
);

/// Provider for unread message count
final unreadMessageCountProvider = FutureProvider<int>((ref) async {
  final currentUserId = await SessionService.getUserId();
  if (currentUserId != null) {
    return await ChatService.getUnreadMessageCount(currentUserId.toString());
  }
  return 0;
});

/// Provider for chat typing status (userId -> isTyping), real-time from Firestore
final typingStatusProvider = StreamProvider.family<Map<String, bool>, String>((
  ref,
  chatId,
) {
  return ChatService.getTypingStatusStream(chatId);
});

/// Provider for online users
final onlineUsersProvider = StateProvider<Set<String>>((ref) => {});

/// Provider for message selection mode
final messageSelectionModeProvider = StateProvider<bool>((ref) => false);

/// Provider for chat search query
final chatSearchQueryProvider = StateProvider<String>((ref) => '');

/// Provider for filtered individual chats based on search
final filteredChatsProvider = Provider<AsyncValue<List<Chat>>>((ref) {
  final chatsAsync = ref.watch(individualChatsStreamProvider);
  final searchQuery = ref.watch(chatSearchQueryProvider);

  return chatsAsync.when(
    data: (chats) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(chats);
      }

      final filteredChats =
          chats.where((chat) {
            // Filter by chat name or last message
            final chatName = chat.getDisplayName('').toLowerCase();
            final lastMessageText = chat.lastMessage?.text?.toLowerCase() ?? '';
            final query = searchQuery.toLowerCase();

            return chatName.contains(query) || lastMessageText.contains(query);
          }).toList();

      return AsyncValue.data(filteredChats);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

/// Provider for filtered group chats based on search
final filteredGroupChatsProvider = Provider<AsyncValue<List<Chat>>>((ref) {
  final chatsAsync = ref.watch(groupChatsStreamProvider);
  final searchQuery = ref.watch(chatSearchQueryProvider);

  return chatsAsync.when(
    data: (chats) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(chats);
      }

      final filteredChats =
          chats.where((chat) {
            // Filter by group name or last message
            final groupName = (chat.groupName ?? '').toLowerCase();
            final lastMessageText = chat.lastMessage?.text?.toLowerCase() ?? '';
            final query = searchQuery.toLowerCase();

            return groupName.contains(query) || lastMessageText.contains(query);
          }).toList();

      return AsyncValue.data(filteredChats);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

/// Hybrid messages provider that combines Firebase and offline functionality
final hybridMessagesStreamProvider = StreamProvider.family.autoDispose<
  List<Message>,
  String
>((ref, chatId) async* {
  try {
    // Always try Firebase first, regardless of connectivity status
    await for (final messages in ChatService.getMessagesStream(chatId)) {
      // Get current user ID to filter deleted messages
      final currentUserId = await SessionService.getUserId();
      final currentUserIdString = currentUserId?.toString();

      final filteredMessages =
          currentUserIdString != null
              ? messages
                  .where((message) => !message.isDeletedBy(currentUserIdString))
                  .toList()
              : messages;

      // Store received messages locally for offline access
      try {
        for (final message in filteredMessages) {
          await HiveChatService.storeMessage(
            LocalMessage.fromMessage(message, chatId),
          );
        }
      } catch (e) {
        debugPrint('Error storing messages locally: $e');
      }

      // Preload images from new messages in background
      final messageNotifier = ref.read(messageProvider(chatId).notifier);
      messageNotifier._preloadImagesFromNewMessages(filteredMessages);

      // Handle WhatsApp receiver storage for new image messages
      messageNotifier._handleWhatsAppReceiverStorage(
        filteredMessages,
        currentUserIdString,
      );

      yield filteredMessages;
    }
  } catch (e) {
    // If Firebase fails, fall back to local database
    debugPrint('Firebase stream failed, falling back to local storage: $e');
    try {
      final localMessages = await HiveChatService.getMessagesForChat(chatId);
      final messages =
          localMessages.map((localMsg) => localMsg.toMessage()).toList();

      // Sort by timestamp (newest first)
      messages.sort((a, b) => b.timestamp.compareTo(a.timestamp));
 
      // Filter out messages that are deleted by the current user (same as online filtering)
      final currentUserId = await SessionService.getUserId();
      final currentUserIdString = currentUserId?.toString();

      final filteredMessages =
          currentUserIdString != null
              ? messages
                  .where((message) => !message.isDeletedBy(currentUserIdString))
                  .toList()
              : messages;

      debugPrint(
        '📱 Offline: Loaded ${localMessages.length} messages, filtered to ${filteredMessages.length} (removed ${localMessages.length - filteredMessages.length} deleted messages)',
      );

      yield filteredMessages;
    } catch (localError) {
      debugPrint('Error loading offline messages: $localError');
      yield [];
    }
  }
});

// ==================== UTILITY PROVIDERS ====================

/// Provider for formatting message time
final messageTimeProvider = Provider.family<String, DateTime>((ref, timestamp) {
  final now = DateTime.now();
  final difference = now.difference(timestamp);

  if (difference.inDays > 0) {
    return '${difference.inDays}d ago';
  } else if (difference.inHours > 0) {
    return '${difference.inHours}h ago';
  } else if (difference.inMinutes > 0) {
    return '${difference.inMinutes}m ago';
  } else {
    return 'Just now';
  }
});

/// Provider for formatting file size
final fileSizeProvider = Provider.family<String, int>((ref, bytes) {
  if (bytes < 1024) {
    return '$bytes B';
  } else if (bytes < 1024 * 1024) {
    return '${(bytes / 1024).toStringAsFixed(1)} KB';
  } else if (bytes < 1024 * 1024 * 1024) {
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  } else {
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
});

/// Provider for image cache information (for debugging)
final imageCacheInfoProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  return await EnhancedImageCache.getCacheInfo();
});

/// Provider for clearing image cache
final clearImageCacheProvider = Provider<VoidCallback>((ref) {
  return () {
    EnhancedImageCache.clearMemoryCache();
  };
});
