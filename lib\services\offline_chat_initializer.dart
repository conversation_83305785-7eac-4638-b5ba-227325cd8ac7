import 'package:flutter/foundation.dart';
import 'package:mr_garments_mobile/services/hive_chat_service.dart';
import 'package:mr_garments_mobile/services/connectivity_service.dart';
import 'package:mr_garments_mobile/services/offline_chat_service.dart';
import 'package:mr_garments_mobile/services/whatsapp_local_storage_service.dart';

/// Service to initialize all offline chat components
class OfflineChatInitializer {
  static bool _initialized = false;

  /// Initialize all offline chat services
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      debugPrint('🚀 Initializing offline chat system...');

      // Initialize Hive database
      debugPrint('📦 Initializing Hive database...');
      await HiveChatService.initialize();

      // Initialize connectivity service
      debugPrint('📶 Initializing connectivity service...');
      await ConnectivityService.instance.initialize();

      // Initialize WhatsApp local storage
      debugPrint('💾 Initializing local storage...');
      await WhatsAppLocalStorageService.initialize();

      // Initialize offline chat service
      debugPrint('💬 Initializing offline chat service...');
      await OfflineChatService.instance.initialize();

      _initialized = true;
      debugPrint('✅ Offline chat system initialized successfully');

      // Print initialization summary
      await _printInitializationSummary();
    } catch (e) {
      debugPrint('❌ Failed to initialize offline chat system: $e');
      rethrow;
    }
  }

  /// Print initialization summary with statistics
  static Future<void> _printInitializationSummary() async {
    try {
      final stats = await HiveChatService.getDatabaseStats();
      final connectivityService = ConnectivityService.instance;

      debugPrint('📊 Offline Chat System Summary:');
      debugPrint('   📱 Total messages in DB: ${stats['totalMessages'] ?? 0}');
      debugPrint('   💬 Total chats: ${stats['totalChats'] ?? 0}');
      debugPrint('   📤 Unsent messages: ${stats['unsentMessages'] ?? 0}');
      debugPrint('   🔄 Sync queue items: ${stats['syncQueueItems'] ?? 0}');
      debugPrint(
        '   📶 Connectivity: ${connectivityService.isConnected ? "ONLINE" : "OFFLINE"}',
      );
      debugPrint(
        '   🌐 Connection type: ${connectivityService.getConnectivityStatusText()}',
      );
    } catch (e) {
      debugPrint('❌ Error getting initialization summary: $e');
    }
  }

  /// Check if offline chat system is initialized
  static bool get isInitialized => _initialized;

  /// Get system health status
  static Future<Map<String, dynamic>> getSystemHealth() async {
    try {
      final stats = await HiveChatService.getDatabaseStats();
      final connectivityService = ConnectivityService.instance;
      final offlineChatService = OfflineChatService.instance;

      return {
        'initialized': _initialized,
        'isOnline': connectivityService.isConnected,
        'connectivityType': connectivityService.currentConnectivity.name,
        'isSyncInProgress': offlineChatService.isSyncInProgress,
        'totalMessages': stats['totalMessages'] ?? 0,
        'totalChats': stats['totalChats'] ?? 0,
        'unsentMessages': stats['unsentMessages'] ?? 0,
        'syncQueueItems': stats['syncQueueItems'] ?? 0,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'initialized': _initialized,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Cleanup all services (call when app is closing)
  static Future<void> cleanup() async {
    try {
      debugPrint('🧹 Cleaning up offline chat system...');

      // Dispose services
      OfflineChatService.instance.dispose();
      ConnectivityService.instance.dispose();

      // Close Hive boxes
      await HiveChatService.close();

      _initialized = false;
      debugPrint('✅ Offline chat system cleanup completed');
    } catch (e) {
      debugPrint('❌ Error during cleanup: $e');
    }
  }
}
