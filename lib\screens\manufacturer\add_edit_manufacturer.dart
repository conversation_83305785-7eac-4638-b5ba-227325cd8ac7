import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/manufacturer_provider.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class AddEditManufacturer extends ConsumerStatefulWidget {
  final int? manufacturerId; // null means ADD mode
  const AddEditManufacturer({super.key, this.manufacturerId});

  @override
  ConsumerState<AddEditManufacturer> createState() =>
      _AddEditManufacturerState();
}

class _AddEditManufacturerState extends ConsumerState<AddEditManufacturer> {
  final _formKey = GlobalKey<FormState>();
  final _contactPersonController = TextEditingController();
  final _mobileController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _brandController = TextEditingController();

  final List<String> _brands = [];
  bool _obscurePassword = true;
  bool _isPasswordChanged = false;
  bool get isEditMode => widget.manufacturerId != null;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (isEditMode) {
      _loadManufacturerData();
    }
  }

  void _loadManufacturerData() async {
    final details = await ref
        .read(manufacturersProvider.notifier)
        .editManufacturerDetails(widget.manufacturerId!);
    setState(() {
      _contactPersonController.text = details['contactPerson'] ?? '';
      _mobileController.text = details['mobile'] ?? '';
      _emailController.text = details['email'] ?? '';
      _addressController.text = details['address'] ?? '';
      _usernameController.text = details['username'] ?? '';
      _passwordController.text = details['password'] ?? '';
      _brands.clear();
      _brands.addAll(List<String>.from(details['brands'] ?? []));
    });
  }

  @override
  void dispose() {
    _contactPersonController.dispose();
    _mobileController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  InputDecoration getDecoration(String label) => InputDecoration(
    labelText: label,
    filled: true,
    fillColor: Colors.grey.shade100,
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: BorderSide.none,
    ),
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: const Color(0xFF005368),
        foregroundColor: Colors.white,
        title: Text(
          isEditMode ? 'Edit Manufacturer' : 'Add Manufacturer',
          style: GoogleFonts.poppins(color: Colors.white, fontSize: 18),
        ),
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _contactPersonController,
                            decoration: getDecoration('Contact Person Name'),
                            validator:
                                (value) =>
                                    value == null || value.isEmpty
                                        ? 'Enter contact person name'
                                        : null,
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _mobileController,
                            keyboardType: TextInputType.phone,
                            decoration: getDecoration('Mobile No.'),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter mobile number';
                              }
                              if (!RegExp(r'^\d{10}$').hasMatch(value)) {
                                return 'Enter valid 10-digit number';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                            decoration: getDecoration('Email ID'),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter email';
                              }
                              if (!RegExp(
                                r'^[^@]+@[^@]+\.[^@]+',
                              ).hasMatch(value)) {
                                return 'Enter valid email';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _addressController,
                            decoration: getDecoration('Address'),
                            validator:
                                (value) =>
                                    value == null || value.isEmpty
                                        ? 'Please enter address'
                                        : null,
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _usernameController,
                            decoration: getDecoration('Username'),
                            validator:
                                (value) =>
                                    value == null || value.isEmpty
                                        ? 'Enter username'
                                        : null,
                          ),
                          const SizedBox(height: 16),
                          // if (!isEditMode) ...[
                          TextFormField(
                            controller: _passwordController,
                            decoration: getDecoration('Password').copyWith(
                              suffixIcon: IconButton(
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                                icon: Icon(
                                  _obscurePassword
                                      ? Icons.visibility_off
                                      : Icons.visibility,
                                ),
                              ),
                            ),
                            obscureText: _obscurePassword,
                            validator: (v) {
                              if (!isEditMode && (v == null || v.length < 6)) {
                                return 'Min. 6 characters required';
                              }
                              return null;
                            },
                            onChanged: (_) {
                              if (isEditMode) {
                                setState(() {
                                  _isPasswordChanged = true;
                                });
                              }
                            },
                          ),
                          const SizedBox(height: 16),
                          // ],
                          // Brands Field
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _brandController,
                                  decoration: getDecoration('Add Brand'),
                                ),
                              ),
                              const SizedBox(width: 8),
                              ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF005368),
                                ),
                                onPressed: () {
                                  final brand = _brandController.text.trim();
                                  if (brand.isNotEmpty) {
                                    setState(() {
                                      _brands.add(brand);
                                      _brandController.clear();
                                    });
                                  }
                                },
                                child: const Text(
                                  "Add",
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Wrap(
                            spacing: 8,
                            children:
                                _brands
                                    .map(
                                      (brand) => Chip(
                                        label: Text(brand),
                                        onDeleted:
                                            () => setState(
                                              () => _brands.remove(brand),
                                            ),
                                      ),
                                    )
                                    .toList(),
                          ),
                          const SizedBox(height: 46),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              icon: const Icon(Icons.save),
                              label: Text(
                                isEditMode
                                    ? "Update Manufacturer"
                                    : "Create Manufacturer",
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFFF2A738),
                                padding: const EdgeInsets.symmetric(
                                  vertical: 14,
                                ),
                              ),
                              onPressed: () async {
                                if (_formKey.currentState!.validate()) {
                                  setState(
                                    () => _isLoading = true,
                                  ); // show loader
                                  final data = {
                                    "contactPerson":
                                        _contactPersonController.text.trim(),
                                    "username": _usernameController.text.trim(),
                                    "brands": _brands,
                                    "email": _emailController.text.trim(),
                                    // "password": _passwordController.text.trim(),
                                    "address": _addressController.text.trim(),
                                    "mobile": _mobileController.text.trim(),
                                  };

                                  if (!isEditMode || _isPasswordChanged) {
                                    data["password"] =
                                        _passwordController.text.trim();
                                  }
                                  final notifier = ref.read(
                                    manufacturersProvider.notifier,
                                  );

                                  try {
                                    if (isEditMode) {
                                      // if (_isPasswordChanged &&
                                      //     _passwordController.text
                                      //             .trim()
                                      //             .length >=
                                      //         6) {
                                      //   data["password"] =
                                      //       _passwordController.text.trim();
                                      // }
                                      await notifier.updateManufacturer(
                                        widget.manufacturerId!,
                                        data,
                                      );
                                    } else {
                                      await notifier.addManufacturer(data);
                                    }
                                    if (!context.mounted) return;
                                    // Navigator.pushAndRemoveUntil(
                                    //   context,
                                    //   MaterialPageRoute(
                                    //     builder:
                                    //         (_) =>
                                    //             const ManufacturerTabpageView(),
                                    //   ),
                                    //   (route) => false,
                                    // );
                                    Navigator.pop(context);
                                  } catch (e) {
                                    if (context.mounted) {
                                      AppSnackbar.showError(
                                        context,
                                        "Error: $e",
                                      );
                                    }
                                  } finally {
                                    if (mounted) {
                                      setState(
                                        () => _isLoading = false,
                                      ); // hide loader
                                    }
                                  }
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (_isLoading)
                    Container(
                      color: Colors.black.withAlpha(77),
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
