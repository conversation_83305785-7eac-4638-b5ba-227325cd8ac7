import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Service to monitor network connectivity status
class ConnectivityService {
  static ConnectivityService? _instance;
  static ConnectivityService get instance => _instance ??= ConnectivityService._();
  ConnectivityService._();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  // Stream controllers
  final StreamController<bool> _connectionStatusController = StreamController<bool>.broadcast();
  final StreamController<ConnectivityResult> _connectivityController = StreamController<ConnectivityResult>.broadcast();
  
  // Current status
  bool _isConnected = false;
  ConnectivityResult _currentConnectivity = ConnectivityResult.none;
  
  // Getters
  bool get isConnected => _isConnected;
  ConnectivityResult get currentConnectivity => _currentConnectivity;
  
  // Streams
  Stream<bool> get connectionStatusStream => _connectionStatusController.stream;
  Stream<ConnectivityResult> get connectivityStream => _connectivityController.stream;

  /// Initialize connectivity monitoring
  Future<void> initialize() async {
    try {
      // Check initial connectivity
      final List<ConnectivityResult> connectivityResults = await _connectivity.checkConnectivity();
      final ConnectivityResult result = connectivityResults.isNotEmpty 
          ? connectivityResults.first 
          : ConnectivityResult.none;
      
      await _updateConnectionStatus(result);
      
      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        (List<ConnectivityResult> results) async {
          final ConnectivityResult result = results.isNotEmpty 
              ? results.first 
              : ConnectivityResult.none;
          await _updateConnectionStatus(result);
        },
        onError: (error) {
          debugPrint('❌ Connectivity stream error: $error');
        },
      );
      
      debugPrint('✅ ConnectivityService initialized');
      debugPrint('📶 Initial connectivity: $_currentConnectivity (Connected: $_isConnected)');
    } catch (e) {
      debugPrint('❌ Error initializing ConnectivityService: $e');
      rethrow;
    }
  }

  /// Update connection status based on connectivity result
  Future<void> _updateConnectionStatus(ConnectivityResult result) async {
    _currentConnectivity = result;
    
    // Determine if we have internet connection
    bool hasConnection = false;
    
    switch (result) {
      case ConnectivityResult.wifi:
      case ConnectivityResult.mobile:
      case ConnectivityResult.ethernet:
        // We have a connection, but let's verify with a ping
        hasConnection = await _verifyInternetConnection();
        break;
      case ConnectivityResult.none:
      case ConnectivityResult.bluetooth:
      case ConnectivityResult.vpn:
      case ConnectivityResult.other:
        hasConnection = false;
        break;
    }
    
    // Only update if status changed
    if (_isConnected != hasConnection) {
      _isConnected = hasConnection;
      
      // Notify listeners
      _connectionStatusController.add(_isConnected);
      _connectivityController.add(_currentConnectivity);
      
      debugPrint('📶 Connection status changed: ${_isConnected ? "ONLINE" : "OFFLINE"} ($_currentConnectivity)');
      
      // Trigger sync if we're back online
      if (_isConnected) {
        _onConnectionRestored();
      } else {
        _onConnectionLost();
      }
    }
  }

  /// Verify internet connection by attempting to reach a reliable server
  Future<bool> _verifyInternetConnection() async {
    try {
      // Try to reach Google's DNS server
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 5));
      
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      // If lookup fails, we don't have internet
      return false;
    }
  }

  /// Called when connection is restored
  void _onConnectionRestored() {
    debugPrint('🌐 Internet connection restored - triggering sync');
    // This will be used by other services to trigger sync
  }

  /// Called when connection is lost
  void _onConnectionLost() {
    debugPrint('📵 Internet connection lost - switching to offline mode');
  }

  /// Manually check connectivity (useful for pull-to-refresh)
  Future<bool> checkConnectivity() async {
    try {
      final List<ConnectivityResult> results = await _connectivity.checkConnectivity();
      final ConnectivityResult result = results.isNotEmpty 
          ? results.first 
          : ConnectivityResult.none;
      
      await _updateConnectionStatus(result);
      return _isConnected;
    } catch (e) {
      debugPrint('❌ Error checking connectivity: $e');
      return false;
    }
  }

  /// Get connectivity status as string for UI display
  String getConnectivityStatusText() {
    if (!_isConnected) {
      return 'Offline';
    }
    
    switch (_currentConnectivity) {
      case ConnectivityResult.wifi:
        return 'Connected via WiFi';
      case ConnectivityResult.mobile:
        return 'Connected via Mobile Data';
      case ConnectivityResult.ethernet:
        return 'Connected via Ethernet';
      case ConnectivityResult.vpn:
        return 'Connected via VPN';
      default:
        return 'Connected';
    }
  }

  /// Get connectivity icon for UI display
  String getConnectivityIcon() {
    if (!_isConnected) {
      return '📵';
    }
    
    switch (_currentConnectivity) {
      case ConnectivityResult.wifi:
        return '📶';
      case ConnectivityResult.mobile:
        return '📱';
      case ConnectivityResult.ethernet:
        return '🌐';
      case ConnectivityResult.vpn:
        return '🔒';
      default:
        return '✅';
    }
  }

  /// Wait for connection to be restored (useful for sync operations)
  Future<void> waitForConnection({Duration? timeout}) async {
    if (_isConnected) return;
    
    final completer = Completer<void>();
    late StreamSubscription<bool> subscription;
    
    subscription = connectionStatusStream.listen((isConnected) {
      if (isConnected) {
        subscription.cancel();
        completer.complete();
      }
    });
    
    // Set timeout if provided
    if (timeout != null) {
      Timer(timeout, () {
        if (!completer.isCompleted) {
          subscription.cancel();
          completer.completeError(TimeoutException('Connection timeout', timeout));
        }
      });
    }
    
    return completer.future;
  }

  /// Check if we have a fast connection (WiFi or good mobile)
  bool hasFastConnection() {
    return _isConnected && 
           (_currentConnectivity == ConnectivityResult.wifi || 
            _currentConnectivity == ConnectivityResult.ethernet);
  }

  /// Check if we're on mobile data (for data usage warnings)
  bool isOnMobileData() {
    return _isConnected && _currentConnectivity == ConnectivityResult.mobile;
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectionStatusController.close();
    _connectivityController.close();
    debugPrint('📦 ConnectivityService disposed');
  }
}

/// Connectivity status for easy state management
class ConnectivityStatus {
  final bool isConnected;
  final ConnectivityResult connectivityResult;
  final String statusText;
  final String icon;

  ConnectivityStatus({
    required this.isConnected,
    required this.connectivityResult,
    required this.statusText,
    required this.icon,
  });

  factory ConnectivityStatus.fromService(ConnectivityService service) {
    return ConnectivityStatus(
      isConnected: service.isConnected,
      connectivityResult: service.currentConnectivity,
      statusText: service.getConnectivityStatusText(),
      icon: service.getConnectivityIcon(),
    );
  }

  @override
  String toString() {
    return 'ConnectivityStatus(isConnected: $isConnected, result: $connectivityResult)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConnectivityStatus &&
        other.isConnected == isConnected &&
        other.connectivityResult == connectivityResult;
  }

  @override
  int get hashCode {
    return isConnected.hashCode ^ connectivityResult.hashCode;
  }
}
