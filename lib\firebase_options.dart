// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDep_T4sCv886DT80466UR0SnSTgBjIsEA',
    appId: '1:826553885236:android:3cd54fedae8d97058ea061',
    messagingSenderId: '826553885236',
    projectId: 'mrgarments-f3b34',
    storageBucket: 'mrgarments-f3b34.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCD87xb4Wn0nanWugt885RIrP34HRN_knw',
    appId: '1:826553885236:ios:2de6d0fc328ff2ac8ea061',
    messagingSenderId: '826553885236',
    projectId: 'mrgarments-f3b34',
    storageBucket: 'mrgarments-f3b34.firebasestorage.app',
    iosBundleId: 'com.braincave.mrgarments',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAsWTkze0Egvowd7PW_4FrOjHmfFvEQL3Q',
    appId: '1:826553885236:web:54236dd5301a7bf28ea061',
    messagingSenderId: '826553885236',
    projectId: 'mrgarments-f3b34',
    authDomain: 'mrgarments-f3b34.firebaseapp.com',
    storageBucket: 'mrgarments-f3b34.firebasestorage.app',
    measurementId: 'G-LJM9VWDGH4',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCD87xb4Wn0nanWugt885RIrP34HRN_knw',
    appId: '1:826553885236:ios:5b38bf7ef3e9f65e8ea061',
    messagingSenderId: '826553885236',
    projectId: 'mrgarments-f3b34',
    storageBucket: 'mrgarments-f3b34.firebasestorage.app',
    iosBundleId: 'com.example.mrGarmentsMobile',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAsWTkze0Egvowd7PW_4FrOjHmfFvEQL3Q',
    appId: '1:826553885236:web:6c7d712c7468ccf58ea061',
    messagingSenderId: '826553885236',
    projectId: 'mrgarments-f3b34',
    authDomain: 'mrgarments-f3b34.firebaseapp.com',
    storageBucket: 'mrgarments-f3b34.firebasestorage.app',
    measurementId: 'G-S6XP1N48YP',
  );

}