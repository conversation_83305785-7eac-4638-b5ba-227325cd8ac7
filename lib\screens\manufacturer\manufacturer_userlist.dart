import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/widgets/network_error_widget.dart';
import 'package:mr_garments_mobile/providers/manufacturer_provider.dart';
import 'package:mr_garments_mobile/screens/manufacturer/add_edit_manufacturer.dart';
import 'package:mr_garments_mobile/screens/manufacturer/manufacturer_details.dart';
 
class ManufacturerUserlist extends ConsumerWidget {
  final String searchQuery;
  const ManufacturerUserlist({super.key, required this.searchQuery});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final manufacturerState = ref.watch(manufacturersProvider);

    return manufacturerState.manufacturers.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) {
        final errorText = e.toString();
        final isNetworkError = e is SocketException ||
            errorText.toLowerCase().contains('failed host lookup') ||
            errorText.toLowerCase().contains('socketexception') ||
            errorText.toLowerCase().contains('network');

        if (isNetworkError) {
          return NetworkErrorWidget(
            message: 'Failed to load manufacturers',
            onRetry: () {
              // ignore: unused_result
              ref.refresh(manufacturersProvider);
            },
          );
        }
        return Center(child: Text("Error: $e"));
      },
      data: (manufacturers) {
        final activeManufacturers =
            manufacturers
                .where((m) => m['status']?.toLowerCase() != 'deactivated')
                .toList();

        // apply search filter here

        final filteredManufacturers =
            activeManufacturers.where((m) {
              final name = (m['name'] ?? '').toLowerCase();
              return name.contains(searchQuery.toLowerCase());
            }).toList();

        if (filteredManufacturers.isEmpty) {
          return const Center(child: Text("No manufacturers found"));
        }
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredManufacturers.length,
          itemBuilder: (context, index) {
            final manu = filteredManufacturers[index];
            final brandList = manu['brands'] ?? [];
            return GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => ManufacturerDetailsScreen(
                          manufacturerId: manu['id'],
                        ),
                  ),
                );
              },
              child: Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                elevation: 6,
                shadowColor: Colors.black26,
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const CircleAvatar(
                            backgroundColor: Color(0xFF005368),
                            child: Icon(Icons.business, color: Colors.white),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  manu['name'] ?? '',
                                  style: GoogleFonts.poppins(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                ),
                                Text(
                                  manu['email'] ?? '',
                                  style: GoogleFonts.poppins(
                                    fontSize: 13,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => AddEditManufacturer(
                                        manufacturerId: manu['id'],
                                      ),
                                ),
                              );
                            },
                            icon: const Icon(
                              Icons.edit,
                              color: Color(0xFFF2A738),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      if (manu['address'] != null)
                        Row(
                          children: [
                            const Icon(
                              Icons.location_on,
                              size: 18,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                manu['address'] ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 13,
                                  color: Colors.grey[800],
                                ),
                              ),
                            ),
                          ],
                        ),
                      const SizedBox(height: 6),
                      if (manu['mobile'] != null)
                        Row(
                          children: [
                            const Icon(
                              Icons.phone_android,
                              size: 18,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              manu['mobile'] ?? '',
                              style: GoogleFonts.poppins(
                                fontSize: 13,
                                color: Colors.grey[800],
                              ),
                            ),
                          ],
                        ),
                      const SizedBox(height: 6),
                      if (brandList.isNotEmpty)
                        Row(
                          children: [
                            const Icon(
                              Icons.label_important,
                              size: 18,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              "Brands: ${brandList.join(', ')}",
                              style: GoogleFonts.poppins(
                                fontSize: 13,
                                color: Colors.grey[800],
                              ),
                            ),
                          ],
                        ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _infoBox(
                            label: "Catalog Count",
                            value: "${manu['catalogCount']}",
                          ),
                          _infoBox(
                            label: "Credit Limit",
                            value: "${manu['creditLimit']}",
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _infoBox({required String label, required String value}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: const Color.fromARGB(128, 225, 239, 247),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(fontSize: 12, color: Colors.black54),
          ),
          const SizedBox(height: 4),
          Text(value, style: GoogleFonts.poppins(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }
}
