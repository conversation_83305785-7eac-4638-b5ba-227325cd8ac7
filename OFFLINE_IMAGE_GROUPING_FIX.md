# Offline Image Grouping Fix

## Problem Description
When using the offline chat functionality, the receiver side only shows one image from a group of images, while the sender can see all images correctly. This happens because the offline chat system doesn't properly handle image grouping like the online system does.

## Root Cause Analysis
1. **Online System**: Images are grouped together and displayed as a single message bubble with multiple images using the `GroupedImageMessage` widget.
2. **Offline System**: Each image was treated as a separate message, causing the receiver to only see one image from a group.
3. **Missing Logic**: The offline chat screen didn't have the image grouping logic that exists in the online chat system.

## Solution Implemented

### 1. Enhanced Offline Chat Service
**File**: `lib/services/offline_chat_service.dart`

Added new method to handle message groups:
```dart
/// Store multiple received messages as a group (for image grouping)
Future<void> storeReceivedMessageGroup(List<Message> messages, String chatId) async {
  // Store all messages in the group
  final localMessages = messages.map((message) => LocalMessage.fromMessage(message, chatId)).toList();
  await HiveChatService.storeMessages(localMessages);

  // Download and store all images in the group
  for (final message in messages) {
    if (message.type == MessageType.image && message.mediaUrl != null) {
      // Store each image locally
    }
  }
}
```

### 2. Updated Offline Chat Screen
**File**: `lib/screens/chat/offline_chat_screen.dart`

#### Added Image Grouping Logic
- **Import**: Added `GroupedImageMessage` widget import
- **New Method**: `_buildImageMessage()` - Handles image grouping logic
- **Grouping Logic**: 
  - Identifies first image in a group
  - Collects all subsequent images from the same sender within 1 minute
  - Uses `GroupedImageMessage` widget to display grouped images
  - Hides subsequent images in the group (shows only the grouped view)

#### Key Features:
```dart
Widget _buildImageMessage(Message message, OfflineChatNotifier chatNotifier) {
  // Check if this is the first image in a group
  bool isFirstInGroup = true;
  if (messageIndex > 0) {
    final prevMessage = allMessages[messageIndex - 1];
    if (prevMessage.senderId == message.senderId &&
        prevMessage.type == MessageType.image &&
        message.timestamp.difference(prevMessage.timestamp).inMinutes.abs() <= 1) {
      isFirstInGroup = false;
    }
  }
  
  if (!isFirstInGroup) {
    // Hide subsequent images in group
    return const SizedBox.shrink();
  }
  
  // Collect all images in the group
  // Display using GroupedImageMessage widget
}
```

### 3. Enhanced Offline Chat Provider
**File**: `lib/providers/offline_chat_provider.dart`

Added method to determine current user:
```dart
/// Check if message is sent by current user
Future<bool> isMessageFromCurrentUser(Message message) async {
  final currentUserId = await SessionService.getUserId();
  return message.senderId == currentUserId.toString();
}
```

## How It Works

### Image Grouping Logic
1. **Detection**: When rendering a message, check if it's an image message
2. **First Image Check**: Determine if this is the first image in a group by checking:
   - Previous message is from same sender
   - Previous message is also an image
   - Time difference is within 1 minute
3. **Group Collection**: If it's the first image, collect all subsequent images in the group
4. **Display**: Use `GroupedImageMessage` widget to display all images in the group
5. **Hide Subsequent**: Hide individual images that are part of a group

### Time-based Grouping
- Images sent within 1 minute of each other are considered part of the same group
- Only the first image in a group is displayed (with all images in the group)
- Subsequent images in the group are hidden

### Local Storage Integration
- All images in a group are stored locally using `WhatsAppLocalStorageService`
- Local paths are used when available, falling back to remote URLs
- Proper handling of both sender and receiver images

## Benefits

1. **Consistent Experience**: Offline chat now behaves like online chat for image grouping
2. **Better UX**: Users see all images in a group, not just one
3. **WhatsApp-like Behavior**: Matches the expected behavior of modern chat apps
4. **Backward Compatible**: Doesn't break existing functionality
5. **Performance**: Efficient grouping logic that doesn't impact performance

## Testing

To test the fix:

1. **Setup**: Use two Android phones with the app
2. **Send Images**: Send multiple images quickly (within 1 minute) from one phone
3. **Go Offline**: Turn off internet on the receiving phone
4. **Check Display**: The receiving phone should now show all images in a grouped view
5. **Verify**: All images should be visible in the group, not just one

## Files Modified

1. `lib/services/offline_chat_service.dart` - Added message group handling
2. `lib/screens/chat/offline_chat_screen.dart` - Added image grouping logic
3. `lib/providers/offline_chat_provider.dart` - Added current user detection
4. `OFFLINE_IMAGE_GROUPING_FIX.md` - This documentation

## Future Enhancements

1. **Configurable Grouping Time**: Make the 1-minute grouping window configurable
2. **Visual Indicators**: Add visual indicators for grouped images
3. **Group Management**: Allow users to ungroup images if needed
4. **Performance Optimization**: Cache grouped image data for better performance

## Conclusion

This fix resolves the offline image grouping issue by implementing the same grouping logic used in the online chat system. The solution ensures that when users are offline, they can see all images in a group just like they would when online, providing a consistent and expected user experience.