import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';
import 'package:mr_garments_mobile/widgets/invoice_detail_screen.dart';

class InvoiceTab extends StatelessWidget {
  const InvoiceTab({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 3, // Replace with actual data count
      itemBuilder: (context, index) {
        return Card(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          elevation: 3,
          margin: const EdgeInsets.only(bottom: 16),
          child: ListTile(
            leading: const Icon(Icons.receipt_long_outlined, color: Color(0xFF005368), size: 32),
            title: Text(
              "INVOICE${20000 + index}",
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            subtitle: Text(
              "04/07/2022    ₹${(20000 * (index + 1)).toStringAsFixed(2)}",
              style: GoogleFonts.poppins(fontSize: 12),
            ),
            trailing: ElevatedButton(
              onPressed: () {
                // Navigator.push(context, MaterialPageRoute(builder: (context)=> InvoiceDetailScreen()));
                AppSnackbar.showInfo(context, "This feature is under development!");
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFF2A738),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              child: Text("View", style: GoogleFonts.poppins(fontSize: 12)),
            ),
          ),
        );
      },
    );
  }
}