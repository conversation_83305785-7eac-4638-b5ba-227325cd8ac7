import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:mr_garments_mobile/models/local_message.dart';
import 'package:mr_garments_mobile/services/hive_chat_service.dart';

void main() {
  group('Basic Offline Chat Tests', () {
    setUpAll(() async {
      // Initialize Hive for testing
      await Hive.initFlutter();
      
      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(LocalMessageAdapter());
      }
    });

    tearDownAll(() async {
      // Clean up after tests
      await Hive.deleteFromDisk();
    });

    setUp(() async {
      // Clear all data before each test
      await HiveChatService.clearAllData();
    });

    test('should initialize Hive chat service', () async {
      // Test initialization
      await HiveChatService.initialize();
      
      // Verify boxes are opened
      expect(Hive.isBoxOpen('chat_messages'), true);
      expect(Hive.isBoxOpen('chats_metadata'), true);
      expect(Hive.isBoxOpen('sync_queue'), true);
    });

    test('should store and retrieve a simple message', () async {
      // Initialize the service
      await HiveChatService.initialize();

      // Create a simple test message
      final testMessage = LocalMessage(
        id: 'test_message_1',
        senderId: 'user_123',
        senderName: 'Test User',
        chatId: 'chat_123',
        messageType: 0, // 0 for text message
        text: 'Hello, this is a test message',
        timestamp: DateTime.now().millisecondsSinceEpoch,
        messageStatus: 1, // 1 for sent status
        isSent: true,
        isLocalOnly: false,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      // Store the message
      await HiveChatService.storeMessage(testMessage);

      // Retrieve messages for the chat
      final messages = await HiveChatService.getMessagesForChat('chat_123');

      // Verify the message was stored and retrieved correctly
      expect(messages.length, 1);
      expect(messages.first.id, 'test_message_1');
      expect(messages.first.text, 'Hello, this is a test message');
      expect(messages.first.chatId, 'chat_123');
      expect(messages.first.isSent, true);
    });

    test('should get database statistics', () async {
      // Initialize the service
      await HiveChatService.initialize();

      // Initially should be empty
      final initialStats = await HiveChatService.getDatabaseStats();
      expect(initialStats['totalMessages'], 0);
      expect(initialStats['unsentMessages'], 0);
      expect(initialStats['syncQueueItems'], 0);

      // Add a message
      final message = LocalMessage(
        id: 'stats_test_msg',
        senderId: 'user_1',
        senderName: 'User 1',
        chatId: 'chat_1',
        messageType: 0,
        text: 'Stats test message',
        timestamp: DateTime.now().millisecondsSinceEpoch,
        messageStatus: 1,
        isSent: true,
        isLocalOnly: false,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      await HiveChatService.storeMessage(message);

      // Check updated stats
      final updatedStats = await HiveChatService.getDatabaseStats();
      expect(updatedStats['totalMessages'], 1);
    });

    test('should clear all data', () async {
      // Initialize the service
      await HiveChatService.initialize();

      // Add some test data
      final testMessage = LocalMessage(
        id: 'clear_test',
        senderId: 'user_123',
        senderName: 'Test User',
        chatId: 'chat_clear',
        messageType: 0,
        text: 'This will be cleared',
        timestamp: DateTime.now().millisecondsSinceEpoch,
        messageStatus: 1,
        isSent: true,
        isLocalOnly: false,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      await HiveChatService.storeMessage(testMessage);

      // Verify data exists
      final messagesBefore = await HiveChatService.getMessagesForChat('chat_clear');
      expect(messagesBefore.length, 1);

      // Clear all data
      await HiveChatService.clearAllData();

      // Verify data is cleared
      final messagesAfter = await HiveChatService.getMessagesForChat('chat_clear');
      expect(messagesAfter.length, 0);

      final stats = await HiveChatService.getDatabaseStats();
      expect(stats['totalMessages'], 0);
    });
  });
}
