import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/models/message.dart';

class CatalogMessageWidget extends StatelessWidget {
  final Message message;
  final bool isSentByMe;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isOnline;

  const CatalogMessageWidget({
    super.key,
    required this.message,
    required this.isSentByMe,
    this.onTap,
    this.onLongPress,
    this.isOnline = true,
  });

  @override
  Widget build(BuildContext context) {
    // Safely extract catalog data with error handling
    List<dynamic> catalogs = [];
    Map<String, dynamic>? catalog;

    try {
      catalogs = message.metadata?['catalogs'] as List<dynamic>? ?? [];
      catalog =
          catalogs.isNotEmpty ? catalogs.first as Map<String, dynamic>? : null;
    } catch (e) {
      // If there's an error parsing catalog data, return empty widget
      debugPrint('Error parsing catalog data: $e');
      return const SizedBox.shrink();
    }

    // If no valid catalog data, don't render
    if (catalog == null) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        margin: EdgeInsets.only(
          left: isSentByMe ? 10 : 8,
          right: isSentByMe ? 8 : 10,
          bottom: 8,
        ),
        child: Row(
          mainAxisAlignment:
              isSentByMe ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (!isSentByMe) ...[
              const SizedBox(width: 6),
            ],
            Flexible(
              child: Container(
                constraints: const BoxConstraints(maxWidth: 280),
                decoration: BoxDecoration(
                  color:
                      isSentByMe
                          ? const Color(0xFF005368)
                          : const Color.fromARGB(255, 242, 248, 255),
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(20),
                    topRight: const Radius.circular(20),
                    bottomLeft: Radius.circular(isSentByMe ? 20 : 4),
                    bottomRight: Radius.circular(isSentByMe ? 4 : 20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Catalog details
                    Container(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors:
                                    isSentByMe
                                        ? [
                                          Colors.white.withOpacity(0.3),
                                          Colors.white.withOpacity(0.1),
                                        ]
                                        : [
                                          const Color(0xFF005368),
                                          const Color(0xFF007B8A),
                                        ],
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.photo_library_rounded,
                              color: isSentByMe ? Colors.white : Colors.white,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  catalog['catalogNumber'] ?? 'Unknown Catalog',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color:
                                        isSentByMe
                                            ? Colors.white
                                            : Colors.grey[800],
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: (isSentByMe
                                                ? Colors.white
                                                : const Color(0xFF005368))
                                            .withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Text(
                                        catalog['brandName'] ?? 'Unknown Brand',
                                        style: GoogleFonts.poppins(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w600,
                                          color:
                                              isSentByMe
                                                  ? Colors.white
                                                  : const Color(0xFF005368),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: (isSentByMe
                                                ? Colors.white
                                                : const Color(0xFFF2A738))
                                            .withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.photo_camera_rounded,
                                            size: 12,
                                            color:
                                                isSentByMe
                                                    ? Colors.white
                                                    : const Color(0xFFF2A738),
                                          ), 
                                          const SizedBox(width: 4),
                                          Text(
                                            '${catalog['photosCount'] ?? 0}',
                                            style: GoogleFonts.poppins(
                                              fontSize: 10,
                                              fontWeight: FontWeight.w600,
                                              color:
                                                  isSentByMe
                                                      ? Colors.white
                                                      : const Color(0xFFF2A738),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Action button with improved design
                    Container(
                      margin: const EdgeInsets.all(12),
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed:
                            isOnline
                                ? onTap
                                : () {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: const Text(
                                        'Please connect to the internet to view catalog',
                                      ),
                                      backgroundColor: Colors.red.shade600,
                                      duration: const Duration(seconds: 2),
                                    ),
                                  );
                                },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              isOnline
                                  ? (isSentByMe
                                      ? Colors.white.withValues(alpha: 0.2)
                                      : const Color(0xFF005368))
                                  : Colors.grey.shade400,
                          foregroundColor:
                              isOnline
                                  ? (isSentByMe ? Colors.white : Colors.white)
                                  : Colors.grey.shade600,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        icon: Icon(
                          isOnline
                              ? Icons.visibility_rounded
                              : Icons.cloud_off_rounded,
                          size: 16,
                        ),
                        label: Text(
                          isOnline ? 'View Catalog' : 'Offline',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  
}