import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/providers/offline_chat_provider.dart';
import 'package:mr_garments_mobile/services/offline_chat_initializer.dart';
import 'package:mr_garments_mobile/services/hive_chat_service.dart';

/// Demo screen to test offline chat functionality
class OfflineChatDemo extends ConsumerStatefulWidget {
  const OfflineChatDemo({super.key});

  @override
  ConsumerState<OfflineChatDemo> createState() => _OfflineChatDemoState();
}

class _OfflineChatDemoState extends ConsumerState<OfflineChatDemo> {
  final TextEditingController _messageController = TextEditingController();
  final String _demoCharId = 'demo_chat_123';
  bool _initialized = false;

  @override
  void initState() {
    super.initState();
    _initializeDemo();
  }

  Future<void> _initializeDemo() async {
    try {
      await OfflineChatInitializer.initialize();
      setState(() {
        _initialized = true;
      });
    } catch (e) {
      debugPrint('❌ Error initializing demo: $e');
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_initialized) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Offline Chat Demo'),
          backgroundColor: const Color(0xFF005368),
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Color(0xFF005368)),
              SizedBox(height: 16),
              Text('Initializing offline chat system...'),
            ],
          ),
        ),
      );
    }

    final connectivityStatus = ref.watch(connectivityStatusProvider);
    final databaseStats = ref.watch(chatDatabaseStatsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Offline Chat Demo'),
        backgroundColor: const Color(0xFF005368),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(LucideIcons.refreshCw),
            onPressed: _refreshStats,
            tooltip: 'Refresh Stats',
          ),
        ],
      ),
      body: Column(
        children: [
          // Status cards
          _buildStatusCards(connectivityStatus, databaseStats),

          // Demo actions
          _buildDemoActions(),

          // Chat interface
          Expanded(child: _buildChatInterface()),
        ],
      ),
    );
  }

  Widget _buildStatusCards(
    AsyncValue<bool> connectivityStatus,
    AsyncValue<Map<String, int>> databaseStats,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Connectivity status
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    connectivityStatus.value == true
                        ? LucideIcons.wifi
                        : LucideIcons.wifiOff,
                    color:
                        connectivityStatus.value == true
                            ? Colors.green
                            : Colors.red,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Connectivity Status',
                          style: GoogleFonts.inter(fontWeight: FontWeight.w600),
                        ),
                        Text(
                          connectivityStatus.value == true
                              ? 'Online'
                              : 'Offline',
                          style: GoogleFonts.inter(
                            color:
                                connectivityStatus.value == true
                                    ? Colors.green
                                    : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Database stats
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Database Statistics',
                    style: GoogleFonts.inter(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 8),
                  databaseStats.when(
                    data:
                        (stats) => Column(
                          children: [
                            _buildStatRow(
                              'Total Messages',
                              stats['totalMessages'] ?? 0,
                            ),
                            _buildStatRow(
                              'Total Chats',
                              stats['totalChats'] ?? 0,
                            ),
                            _buildStatRow(
                              'Unsent Messages',
                              stats['unsentMessages'] ?? 0,
                            ),
                            _buildStatRow(
                              'Sync Queue Items',
                              stats['syncQueueItems'] ?? 0,
                            ),
                          ],
                        ),
                    loading: () => const CircularProgressIndicator(),
                    error: (error, stack) => Text('Error: $error'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, int value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: GoogleFonts.inter(fontSize: 14)),
          Text(
            value.toString(),
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF005368),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDemoActions() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          Text(
            'Demo Actions',
            style: GoogleFonts.inter(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _simulateOfflineMessage,
                  icon: const Icon(LucideIcons.wifiOff),
                  label: const Text('Send Offline'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _clearDatabase,
                  icon: const Icon(LucideIcons.trash2),
                  label: const Text('Clear DB'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChatInterface() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Chat header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF005368),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(LucideIcons.messageCircle, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  'Demo Chat',
                  style: GoogleFonts.inter(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Messages area
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              child: const Center(
                child: Text(
                  'Messages will appear here\n(Use the offline chat screen for full functionality)',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          ),

          // Message input
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border(top: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'Type a demo message...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: _sendDemoMessage,
                  icon: const Icon(LucideIcons.send),
                  style: IconButton.styleFrom(
                    backgroundColor: const Color(0xFF005368),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _sendDemoMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    _messageController.clear();

    // This would normally use the offline chat provider
    // For demo purposes, we'll just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Demo message: $text'),
        backgroundColor: const Color(0xFF005368),
      ),
    );
  }

  void _simulateOfflineMessage() async {
    // Simulate sending a message while offline
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Simulated offline message sent'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _clearDatabase() async {
    try {
      await HiveChatService.clearAllData();
      _refreshStats();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Database cleared successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error clearing database: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _refreshStats() {
    ref.invalidate(chatDatabaseStatsProvider);
  }
}
