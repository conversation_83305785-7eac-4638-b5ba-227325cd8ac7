import 'package:flutter_test/flutter_test.dart';
import 'package:mr_garments_mobile/models/local_message.dart';
import 'package:mr_garments_mobile/models/message.dart';

void main() {
  group('Offline Chat Models Tests', () {
    test('should create LocalMessage with required fields', () {
      final localMessage = LocalMessage(
        id: 'test_id',
        senderId: 'sender_123',
        senderName: 'Test Sender',
        chatId: 'chat_123',
        messageType: 0, // text message
        timestamp: DateTime.now().millisecondsSinceEpoch,
        messageStatus: 1, // sent
        isSent: true,
        isLocalOnly: false,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      expect(localMessage.id, 'test_id');
      expect(localMessage.senderId, 'sender_123');
      expect(localMessage.senderName, 'Test Sender');
      expect(localMessage.chatId, 'chat_123');
      expect(localMessage.messageType, 0);
      expect(localMessage.isSent, true);
      expect(localMessage.isLocalOnly, false);
    });

    test('should create LocalMessage with optional fields', () {
      final localMessage = LocalMessage(
        id: 'test_id_2',
        senderId: 'sender_456',
        senderName: 'Test Sender 2',
        chatId: 'chat_456',
        messageType: 1, // image message
        text: 'Test message with image',
        mediaUrl: 'https://example.com/image.jpg',
        localPath: '/local/path/image.jpg',
        fileName: 'image.jpg',
        fileSize: 1024,
        timestamp: DateTime.now().millisecondsSinceEpoch,
        messageStatus: 1,
        isSent: true,
        isLocalOnly: false,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      expect(localMessage.text, 'Test message with image');
      expect(localMessage.mediaUrl, 'https://example.com/image.jpg');
      expect(localMessage.localPath, '/local/path/image.jpg');
      expect(localMessage.fileName, 'image.jpg');
      expect(localMessage.fileSize, 1024);
    });

    test('should convert LocalMessage to Message', () {
      final localMessage = LocalMessage(
        id: 'convert_test',
        senderId: 'sender_789',
        senderName: 'Convert Test',
        chatId: 'chat_789',
        messageType: 0,
        text: 'Convert test message',
        timestamp: DateTime.now().millisecondsSinceEpoch,
        messageStatus: 1,
        isSent: true,
        isLocalOnly: false,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      final message = localMessage.toMessage();

      expect(message.id, 'convert_test');
      expect(message.senderId, 'sender_789');
      expect(message.senderName, 'Convert Test');
      expect(message.text, 'Convert test message');
    });

    test('should create LocalMessage from Message', () {
      final message = Message(
        id: 'from_message_test',
        senderId: 'sender_abc',
        senderName: 'From Message Test',
        text: 'Original message text',
        timestamp: DateTime.now(),
        type: MessageType.text,
        status: MessageStatus.sent,
      );

      final localMessage = LocalMessage.fromMessage(message, 'chat_abc');

      expect(localMessage.id, 'from_message_test');
      expect(localMessage.senderId, 'sender_abc');
      expect(localMessage.senderName, 'From Message Test');
      expect(localMessage.text, 'Original message text');
      expect(localMessage.chatId, 'chat_abc');
      expect(localMessage.isSent, true);
      expect(localMessage.isLocalOnly, false);
    });

    test('should handle unsent local-only messages', () {
      final localMessage = LocalMessage(
        id: 'unsent_test',
        senderId: 'sender_unsent',
        senderName: 'Unsent Test',
        chatId: 'chat_unsent',
        messageType: 0,
        text: 'This message is unsent',
        timestamp: DateTime.now().millisecondsSinceEpoch,
        messageStatus: 0, // pending
        isSent: false,
        isLocalOnly: true,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      expect(localMessage.isSent, false);
      expect(localMessage.isLocalOnly, true);
      expect(localMessage.messageStatus, 0); // pending
    });

    test('should handle image messages with local paths', () {
      final imageMessage = LocalMessage(
        id: 'image_test',
        senderId: 'sender_image',
        senderName: 'Image Test',
        chatId: 'chat_image',
        messageType: 1, // image
        mediaUrl: 'https://firebase.com/image.jpg',
        localPath: '/Android/media/com.mrgarments/media/Images/Sent/image.jpg',
        fileName: 'image.jpg',
        fileSize: 2048,
        thumbnailUrl: 'https://firebase.com/thumb.jpg',
        localThumbnailPath:
            '/Android/media/com.mrgarments/media/Images/Sent/thumb.jpg',
        timestamp: DateTime.now().millisecondsSinceEpoch,
        messageStatus: 1,
        isSent: true,
        isLocalOnly: false,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      expect(imageMessage.messageType, 1);
      expect(imageMessage.mediaUrl, 'https://firebase.com/image.jpg');
      expect(
        imageMessage.localPath,
        '/Android/media/com.mrgarments/media/Images/Sent/image.jpg',
      );
      expect(imageMessage.fileName, 'image.jpg');
      expect(imageMessage.fileSize, 2048);
      expect(imageMessage.thumbnailUrl, 'https://firebase.com/thumb.jpg');
      expect(
        imageMessage.localThumbnailPath,
        '/Android/media/com.mrgarments/media/Images/Sent/thumb.jpg',
      );
    });

    test('should handle metadata and reply fields', () {
      final replyMessage = LocalMessage(
        id: 'reply_test',
        senderId: 'sender_reply',
        senderName: 'Reply Test',
        chatId: 'chat_reply',
        messageType: 0,
        text: 'This is a reply',
        timestamp: DateTime.now().millisecondsSinceEpoch,
        messageStatus: 1,
        replyToMessageId: 'original_message_id',
        replyToText: 'Original message text',
        replyToSenderName: 'Original Sender',
        isForwarded: false,
        metadataJson: '{"custom": "data"}',
        isSent: true,
        isLocalOnly: false,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      expect(replyMessage.replyToMessageId, 'original_message_id');
      expect(replyMessage.replyToText, 'Original message text');
      expect(replyMessage.replyToSenderName, 'Original Sender');
      expect(replyMessage.isForwarded, false);
      expect(replyMessage.metadataJson, '{"custom": "data"}');
    });
  });
}
