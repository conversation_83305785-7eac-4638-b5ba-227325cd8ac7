import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class DistributorService {
  static const String baseUrl = 'https://mrgindia.com/api';

  // Helper method to get auth token
  static Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  // Get all distributors
  static Future<List<dynamic>> fetchAllDistributors() async {
    final token = await _getToken();
    final response = await http.get(
      Uri.parse('$baseUrl/distributors'),
      headers: {
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception( 'Failed to load distributors: ${response.statusCode} ${response.body}',);
    }
  }

  // Get distributor details by ID
  static Future<Map<String, dynamic>> fetchDistributorDetails(
    int distributorId,
  ) async {
    final token = await _getToken();
    final response = await http.get(
      Uri.parse('$baseUrl/distributors/$distributorId'),
      headers: {
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to fetch distributor details: ${response.statusCode} ${response.body}',);
    }
  }

  // update distributor status
  static Future<Map<String, dynamic>> updateDistributorStatus(
    int id,
    String status,
  ) async {
    final token = await _getToken();
    final url = Uri.parse('$baseUrl/distributors/$id/status');
    final response = await http.patch(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
      body: jsonEncode({'status': status}),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception( 'Failed to update distributor status: ${response.statusCode} ${response.body}');
    }
  }

  static Future<Map<String, dynamic>> addDistributor(
    Map<String, dynamic> data,
  ) async {
    final token = await _getToken();
    final url = Uri.parse('$baseUrl/distributors');
    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
      body: jsonEncode(data),
    );

    if (response.statusCode == 200 || response.statusCode == 201) {
      return json.decode(response.body);
    } else {
      throw Exception( 'Failed to create distributor: ${response.statusCode} ${response.body}',);
    }
  }

  static Future<Map<String, dynamic>> updateDistributor(
    int id,
    Map<String, dynamic> data,
  ) async {
    final token = await _getToken();
    final url = Uri.parse('$baseUrl/distributors/$id');
    final response = await http.put(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
      body: jsonEncode(data),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception(
        'Failed to update distributor: ${response.statusCode} ${response.body}',
      );
    }
  }

  static Future<Map<String, dynamic>> getDistributorDetails(int id) async {
    final token = await _getToken();
    final url = Uri.parse('$baseUrl/distributors/$id');
    final response = await http.get(url, 
    headers: {
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception( 'Failed to fetch distributor details: ${response.statusCode} ${response.body}',);
    }
  }
}
