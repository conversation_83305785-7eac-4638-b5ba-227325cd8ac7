import 'package:flutter_test/flutter_test.dart';
import 'package:mr_garments_mobile/models/local_message.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/services/hive_chat_service.dart';
import 'package:mr_garments_mobile/services/offline_chat_initializer.dart';

void main() {
  group('Debug Offline Chat', () {
    setUpAll(() async {
      await OfflineChatInitializer.initialize();
    });

    tearDownAll(() async {
      await HiveChatService.clearAllData();
    });

    test('should store and retrieve messages correctly', () async {
      const chatId = 'test_chat_123';
      
      // Create a test message
      final testMessage = Message(
        id: 'msg_1',
        senderId: 'user_1',
        senderName: 'Test User',
        type: MessageType.text,
        text: 'Hello World!',
        status: MessageStatus.sent,
        timestamp: DateTime.now(),
        readBy: [],
        deletedBy: [],
        isLocalOnly: false,
      );

      // Convert to LocalMessage and store
      final localMessage = LocalMessage.fromMessage(testMessage, chatId);
      await HiveChatService.storeMessage(localMessage);

      // Retrieve messages
      final retrievedMessages = await HiveChatService.getMessagesForChat(chatId);
      
      expect(retrievedMessages.length, 1);
      expect(retrievedMessages.first.id, 'msg_1');
      expect(retrievedMessages.first.text, 'Hello World!');
      expect(retrievedMessages.first.chatId, chatId);

      // Convert back to Message
      final convertedMessage = retrievedMessages.first.toMessage();
      expect(convertedMessage.id, testMessage.id);
      expect(convertedMessage.text, testMessage.text);
      expect(convertedMessage.senderId, testMessage.senderId);
      expect(convertedMessage.type, testMessage.type);
    });

    test('should handle multiple messages in same chat', () async {
      const chatId = 'test_chat_456';
      
      // Create multiple test messages
      final messages = [
        Message(
          id: 'msg_1',
          senderId: 'user_1',
          senderName: 'User 1',
          type: MessageType.text,
          text: 'First message',
          status: MessageStatus.sent,
          timestamp: DateTime.now().subtract(const Duration(minutes: 2)),
          readBy: [],
          deletedBy: [],
          isLocalOnly: false,
        ),
        Message(
          id: 'msg_2',
          senderId: 'user_2',
          senderName: 'User 2',
          type: MessageType.text,
          text: 'Second message',
          status: MessageStatus.sent,
          timestamp: DateTime.now().subtract(const Duration(minutes: 1)),
          readBy: [],
          deletedBy: [],
          isLocalOnly: false,
        ),
        Message(
          id: 'msg_3',
          senderId: 'user_1',
          senderName: 'User 1',
          type: MessageType.text,
          text: 'Third message',
          status: MessageStatus.sent,
          timestamp: DateTime.now(),
          readBy: [],
          deletedBy: [],
          isLocalOnly: false,
        ),
      ];

      // Store all messages
      for (final message in messages) {
        final localMessage = LocalMessage.fromMessage(message, chatId);
        await HiveChatService.storeMessage(localMessage);
      }

      // Retrieve messages
      final retrievedMessages = await HiveChatService.getMessagesForChat(chatId);
      
      expect(retrievedMessages.length, 3);
      
      // Convert back to Messages and sort by timestamp (newest first)
      final convertedMessages = retrievedMessages.map((localMsg) => localMsg.toMessage()).toList();
      convertedMessages.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      expect(convertedMessages[0].text, 'Third message');
      expect(convertedMessages[1].text, 'Second message');
      expect(convertedMessages[2].text, 'First message');
    });

    test('should handle empty chat', () async {
      const chatId = 'empty_chat_789';
      
      final retrievedMessages = await HiveChatService.getMessagesForChat(chatId);
      expect(retrievedMessages.length, 0);
    });
  });
}
