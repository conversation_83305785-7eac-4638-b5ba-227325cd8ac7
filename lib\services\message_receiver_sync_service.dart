import 'dart:async';
import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/services/hive_chat_service.dart';
import 'package:mr_garments_mobile/services/connectivity_service.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/models/local_message.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
/// Service to ensure receiver gets all messages even when app is in background
/// Implements polling and sync mechanisms for reliable message delivery
///
/// Production-level features:
/// - Periodic sync every 30 seconds
/// - Immediate sync on app resume
/// - Immediate sync on connectivity restore
/// - Batch processing for efficiency
/// - Error handling and retry logic
/// - Prevents duplicate syncs
class MessageReceiverSyncService with WidgetsBindingObserver {
  static MessageReceiverSyncService? _instance;
  static MessageReceiverSyncService get instance =>
      _instance ??= MessageReceiverSyncService._();
  MessageReceiverSyncService._();

  final ConnectivityService _connectivityService = ConnectivityService.instance;
  Timer? _syncTimer;
  bool _isInitialized = false;
  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  StreamSubscription? _connectivitySubscription;

  /// Initialize the receiver sync service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Add app lifecycle observer
      WidgetsBinding.instance.addObserver(this);

      // Set up periodic sync (every 30 seconds when online)
      _syncTimer = Timer.periodic(
        const Duration(seconds: 30),
        (_) => _syncMessages(),
      );

      // Listen to connectivity changes
      _connectivitySubscription = _connectivityService.connectionStatusStream
          .listen((isConnected) {
            if (isConnected) {
              debugPrint('🌐 Internet connected - syncing messages');
              _syncMessages();
            }
          });

      // Do initial sync
      await _syncMessages();

      _isInitialized = true;
      debugPrint('✅ MessageReceiverSyncService initialized');
    } catch (e) {
      debugPrint('❌ Error initializing MessageReceiverSyncService: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        // App came to foreground - sync messages
        debugPrint('📱 App resumed - syncing messages');

        // If app was in background for more than 1 minute, do aggressive sync
        if (_lastSyncTime != null) {
          final timeSinceLastSync = DateTime.now().difference(_lastSyncTime!);
          if (timeSinceLastSync.inMinutes > 1) {
            debugPrint(
              '⚡ App was in background for ${timeSinceLastSync.inMinutes} minutes - doing aggressive sync',
            );
            // Sync immediately and then again after 5 seconds to catch any missed messages
            _syncMessages();
            Future.delayed(const Duration(seconds: 5), () => _syncMessages());
          } else {
            _syncMessages();
          }
        } else {
          _syncMessages();
        }
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        // App went to background - save last sync time
        _lastSyncTime = DateTime.now();
        break;
    }
  }

  /// Sync messages from Firebase to local storage
  /// This ensures receiver gets all messages even when app was in background
  Future<void> _syncMessages() async {
    if (_isSyncing || !_connectivityService.isConnected) return;

    _isSyncing = true;

    try {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) {
        _isSyncing = false;
        return;
      }

      debugPrint('🔄 Syncing messages for user $currentUserId');

      // Get all chats for the current user using stream (take first value)
      final chatsStream = ChatService.getUserChatsStream(
        currentUserId.toString(),
      );
      final chats = await chatsStream.first;

      if (chats.isEmpty) {
        debugPrint('ℹ No chats found for user');
        _lastSyncTime = DateTime.now();
        _isSyncing = false;
        return;
      }

      int totalNewMessages = 0;
      int totalChatsProcessed = 0;

      for (final chat in chats) {
        try {
          // Get messages from Firebase for this chat
          final firebaseMessages = await _getMessagesFromFirebase(chat.id);

          if (firebaseMessages.isEmpty) continue;

          // Get local messages to check for duplicates
          final localMessages = await HiveChatService.getMessagesForChat(
            chat.id,
            limit: 200, // Check more messages to avoid duplicates
          );
          final localMessageIds = localMessages.map((m) => m.id).toSet();

          // Find new messages that aren't in local storage
          final newMessages =
              firebaseMessages
                  .where((msg) => !localMessageIds.contains(msg.id))
                  .toList();

          if (newMessages.isNotEmpty) {
            debugPrint(
              '📥 Found ${newMessages.length} new messages in chat ${chat.id}',
            );

            // Convert to LocalMessage and save in batch
            final localMessagesToSave =
                newMessages.map((msg) {
                  return LocalMessage.fromMessage(msg, chat.id);
                }).toList();

            // Batch save for efficiency
            final saved = await HiveChatService.storeMessages(
              localMessagesToSave,
            );
            if (saved) {
              totalNewMessages += newMessages.length;
            }
          }

          totalChatsProcessed++;
        } catch (e) {
          debugPrint('❌ Error syncing chat ${chat.id}: $e');
          // Continue with other chats even if one fails
        }
      }

      if (totalNewMessages > 0) {
        debugPrint(
          '✅ Synced $totalNewMessages new messages from $totalChatsProcessed chats',
        );
      } else {
        debugPrint(
          '✅ All messages up to date ($totalChatsProcessed chats checked)',
        );
      }

      _lastSyncTime = DateTime.now();
    } catch (e) {
      debugPrint('❌ Error syncing messages: $e');
    } finally {
      _isSyncing = false;
    }
  }

  /// Get messages from Firebase for a specific chat
  /// Returns Message objects, not raw maps
  Future<List<Message>> _getMessagesFromFirebase(String chatId) async {
    try {
      final snapshot =
          await FirebaseFirestore.instance
              .collection('chats')
              .doc(chatId)
              .collection('messages')
              .orderBy('timestamp', descending: true)
              .limit(200) // Get last 200 messages to ensure we don't miss any
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        // Add document ID to data
        data['id'] = doc.id;
        // Convert to Message object
        return Message.fromMap(data);
      }).toList();
    } catch (e) { 
      debugPrint('❌ Error getting messages from Firebase for chat $chatId: $e');
      return [];
    }
  }

  /// Force sync now (can be called manually)
  Future<void> syncNow() async {
    debugPrint('🔄 Manual sync requested');
    await _syncMessages();
  }

  /// Check if currently syncing
  bool get isSyncing => _isSyncing;

  /// Get last sync time
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Dispose the service
  Future<void> dispose() async {
    if (!_isInitialized) return;

    WidgetsBinding.instance.removeObserver(this);
    _syncTimer?.cancel();
    _syncTimer = null;
    await _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
    _isInitialized = false;

    debugPrint('🔄 MessageReceiverSyncService disposed');
  }
}