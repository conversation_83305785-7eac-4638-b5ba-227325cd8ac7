import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/widgets/network_error_widget.dart';
import 'package:mr_garments_mobile/providers/generic_staff_provider.dart';
import 'package:mr_garments_mobile/providers/user_provider.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class StaffRequestTab extends ConsumerStatefulWidget {
  const StaffRequestTab({super.key});

  @override
  ConsumerState<StaffRequestTab> createState() => _StaffRequestTabState();
}

class _StaffRequestTabState extends ConsumerState<StaffRequestTab> {
  @override
  void initState() {
    super.initState();
    // Fetch staff requests when the widget is first initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(genericStaffProvider.notifier).fetchStaffRequests();
    });
  }

  String _getCompanyTypeDisplay(String? companyType) {
    switch (companyType?.toLowerCase()) {
      case 'manufacturer':
        return 'Manufacturer';
      case 'retailer':
        return 'Retailer';
      case 'distributor':
        return 'Distributor';
      default:
        return 'Unknown';
    }
  }

  void _handleAction(
    BuildContext context,
    WidgetRef ref,
    int staffId,
    String action,
  ) async {
    // showDialog(
    //   context: context,
    //   barrierDismissible: false,
    //   builder: (_) => const Center(child: CircularProgressIndicator()),
    // );

    try {
      // First verify the staff
      await ref
          .read(genericStaffProvider.notifier)
          .verifyStaff(staffId, action);

      // If staff was approved, refresh the users list so they appear in Users tab
      if (action == 'approve') {
        await ref.read(usersProvider.notifier).fetchUsers();
      }

      if (!context.mounted) return;
      Navigator.pop(context);

      AppSnackbar.showSuccess(
        context,
        "Staff ${action == 'approve' ? 'approved' : 'rejected'} successfully",
      );
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context);
        AppSnackbar.showError(context, 'Error: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final requestsAsync = ref.watch(genericStaffRequestsProvider);

    // Debug: Print the current state
    // print('Staff requests state: ${requestsAsync.runtimeType}');
    if (requestsAsync is AsyncData) {
      // print('Staff requests data length: ${requestsAsync.value.length}');
    }

    return requestsAsync.when(
      loading:
          () => const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text("Loading staff requests..."),
              ],
            ),
          ),
      error: (e, _) {
        final errorText = e.toString();
        final isNetworkError =
            e is SocketException ||
            errorText.toLowerCase().contains('failed host lookup') ||
            errorText.toLowerCase().contains('socketexception') ||
            errorText.toLowerCase().contains('network');

        if (isNetworkError) {
          return NetworkErrorWidget(
            message: 'Failed to load staff requests',
            onRetry: () {
              ref.read(genericStaffProvider.notifier).fetchStaffRequests();
            },
          );
        }

        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                "Error loading staff requests",
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text("$e"),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed:
                    () =>
                        ref
                            .read(genericStaffProvider.notifier)
                            .fetchStaffRequests(),
                child: const Text("Retry"),
              ),
            ],
          ),
        );
      },
      data: (requests) {
        if (requests.isEmpty) {
          return const Center(child: Text("No staff requests found"));
        }
        return ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          itemCount: requests.length,
          itemBuilder: (context, index) {
            final staff = requests[index];
            return Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 2,
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: const Color(0xFF005368),
                          child: Text(
                            staff['name']
                                    ?.toString()
                                    .substring(0, 1)
                                    .toUpperCase() ??
                                'S',
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                staff['name']?.toString() ?? 'Unknown',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF005368),
                                ),
                              ),
                              Text(
                                staff['email']?.toString() ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.business,
                                size: 16,
                                color: Colors.blue[700],
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Requested Role: Staff',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.blue[700],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.factory,
                                size: 16,
                                color: Colors.blue[700],
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Company: ${staff['company_name'] ?? 'Unknown Company'}',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  color: Colors.blue[700],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.business_center,
                                size: 16,
                                color: Colors.blue[700],
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Type: ${_getCompanyTypeDisplay(staff['company_type'])}',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  color: Colors.blue[700],
                                ),
                              ),
                            ],
                          ),
                          if (staff['mobile_number'] != null) ...[
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Icon(
                                  Icons.phone,
                                  size: 16,
                                  color: Colors.blue[700],
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Mobile: ${staff['mobile_number']}',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    color: Colors.blue[700],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed:
                                () => _handleAction(
                                  context,
                                  ref,
                                  staff['id'],
                                  'approve',
                                ),
                            icon: const Icon(
                              Icons.check_circle_outline,
                              color: Colors.white,
                              size: 20,
                            ),
                            label: Text(
                              "Approve",
                              style: GoogleFonts.poppins(),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green[600],
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed:
                                () => _handleAction(
                                  context,
                                  ref,
                                  staff['id'],
                                  'reject',
                                ),
                            icon: const Icon(
                              Icons.cancel_outlined,
                              color: Colors.white,
                              size: 20,
                            ),
                            label: Text("Reject", style: GoogleFonts.poppins()),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red[600],
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
