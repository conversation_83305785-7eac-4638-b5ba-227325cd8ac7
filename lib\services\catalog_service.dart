import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as path;
import 'package:shared_preferences/shared_preferences.dart';

class CatalogService {
  static const String baseUrl = 'https://mrgindia.com/api';
  

  // Helper method to get auth token
  static Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  static Future<List<dynamic>> fetchCatalogs() async {
    // Get the auth token
    final token = await _getToken();

    final response = await http.get(Uri.parse('$baseUrl/catalogs'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load catalogs');
    }
  }

  // Get catalog details by ID
  static Future<Map<String, dynamic>> fetchCatalogDetails(int id) async {
    final token = await _getToken();
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/catalogs/$id'),
        headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        // print(
        //   'Error fetching catalog details: ${response.statusCode}, ${response.body}',
        // );
        throw Exception(
          'Failed to fetch catalog details: ${response.statusCode}',
        );
      }
    } catch (e) {
      // print('Exception in fetchCatalogDetails: $e');
      throw Exception('Failed to fetch catalog details: $e');
    }
  }

  // GET catalogsgenerate-number

  static Future<String> generateCatalogNumber() async {
    final token = await _getToken();
    final response = await http.get(
      Uri.parse('$baseUrl/catalogs/generate-number'),headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },

    );
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['catalogNumber'];
    } else {
      throw Exception('Failed to generate catalog number');
    }
  }

  // POST /catalogs
  static Future<Map<String, dynamic>> addCatalog({
    required String brandName,
    required String catalogNumber,
    required int categoryId,
    required int manufacturerId,
    required List<File> images,
    int maxRetries = 2,
  }) async {
    // Get the auth token
    final token = await _getToken();
    // Token retrieved successfully
    if (token == null) {
      throw Exception('Authentication token not found. Please login again.');
    }

    Exception? lastException;

    for (int attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await _performCatalogUpload(
          brandName: brandName,
          catalogNumber: catalogNumber,
          categoryId: categoryId,
          manufacturerId: manufacturerId,
          images: images,
          token: token,
        );
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());

        if (attempt < maxRetries) {
          await Future.delayed(Duration(seconds: (attempt + 1) * 2));
        }
      }
    }

    throw lastException ?? Exception('Upload failed after $maxRetries retries');
  }

  static Future<Map<String, dynamic>> _performCatalogUpload({
    required String brandName,
    required String catalogNumber,
    required int categoryId,
    required int manufacturerId,
    required List<File> images,
    required String token,
  }) async {
    var request = http.MultipartRequest('POST', Uri.parse('$baseUrl/catalogs'));
    request.headers['Accept'] = 'application/json';
    request.headers['Authorization'] = 'Bearer $token';
    request.fields['brandName'] = brandName;
    request.fields['catalogNumber'] = catalogNumber;
    request.fields['categoryId'] = categoryId.toString();
    request.fields['manufacturerId'] = manufacturerId.toString();

    for (var image in images) {
      // Check if file exists and is readable
      if (!await image.exists()) {
        throw Exception('Image file does not exist: ${image.path}');
      }

      final fileLength = await image.length();
      // Uploading image: ${image.path}

      // Validate file size (max 2MB to match server limit)
      if (fileLength > 2 * 1024 * 1024) {
        throw Exception(
          'Image file too large: ${(fileLength / (1024 * 1024)).toStringAsFixed(1)}MB. Maximum allowed is 2MB.',
        );
      }

      final ext = path
          .extension(image.path)
          .toLowerCase()
          .replaceFirst('.', '');
      // print("Image extension: $ext");

      final mimeType =
          {
            'jpg': 'jpeg',
            'jpeg': 'jpeg',
            'png': 'png',
            'heic': 'heic',
            'webp': 'webp',
            'bmp': 'bmp',
            'gif': 'gif',
            '': 'jpeg', // Handle missing extensions
          }[ext] ??
          'jpeg';

      // Using MIME type: image/$mimeType

      try {
        request.files.add(
          await http.MultipartFile.fromPath(
            'images[]',
            image.path,
            contentType: MediaType('image', mimeType),
          ),
        );
      } catch (e) {
        throw Exception(
          'Failed to add image to request: ${image.path}. Error: $e',
        );
      }
    }

    // Sending catalog creation request

    var streamedResponse = await request.send();
    var response = await http.Response.fromStream(streamedResponse);
    // Response received from server

    if (response.statusCode == 200 || response.statusCode == 201) {
      return json.decode(response.body);
    } else if (response.statusCode == 302) {
      throw Exception(
        'Authentication failed - received redirect. Please check your login status.',
      );
    } else if (response.statusCode == 401) {
      throw Exception('Unauthorized - please login again.');
    } else if (response.statusCode == 413) {
      throw Exception('File too large - please reduce image size.');
    } else if (response.statusCode == 422) {
      // Parse validation errors
      try {
        final errorData = json.decode(response.body);
        if (errorData['message'] != null &&
            errorData['message'].toString().contains('2048 kilobytes')) {
          throw Exception('Images are too large. Maximum allowed is 2MB.');
        } else if (errorData['message'] != null) {
          throw Exception('Validation error: ${errorData['message']}');
        }
      } catch (e) {
        // If parsing fails, fall back to generic message
      }
      throw Exception('Validation failed - please check your input.');
    } else {
      throw Exception(
        'Failed to create catalog. Status: ${response.statusCode}, Body: ${response.body}',
      );
    }
  }

  static Future<String> generateShareLink(
    int catalogId,
    String platform,
  ) async {
    final token = await _getToken();
    final response = await http.post(
      Uri.parse('$baseUrl/catalogs/$catalogId/share'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
      body: json.encode({'platform': platform}),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['shareUrl'];
    } else {
      throw Exception('Failed to generate share link: ${response.statusCode}');
    }
  }

  // Add this method to upload more images to an existing catalog
  static Future<Map<String, dynamic>> uploadMoreImages({
    required int catalogId,
    required String brandName,
    required String catalogNumber,
    required String manufacturerName,
    required List<File> images,
  }) async {
    // Get the auth token
    final token = await _getToken();
    if (token == null) {
      throw Exception('Authentication token not found. Please login again.');
    }

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/catalogs/$catalogId/images'),
    );

    request.headers['Accept'] = 'application/json';
    request.headers['Authorization'] = 'Bearer $token';

    // Add required fields
    request.fields['brandName'] = brandName;
    request.fields['catalogNumber'] = catalogNumber;
    request.fields['manufacturerName'] = manufacturerName;

    // Add images
    for (var image in images) {
      if (!await image.exists()) {
        throw Exception('Image file does not exist: ${image.path}');
      }

      final fileLength = await image.length();
      // print("Uploading: ${image.path} | Size: $fileLength");

      final ext = path
          .extension(image.path)
          .toLowerCase()
          .replaceFirst('.', '');
      final mimeType =
          {
            'jpg': 'jpeg',
            'jpeg': 'jpeg',
            'png': 'png',
            'heic': 'heic',
            'webp': 'webp',
            'bmp': 'bmp',
            'gif': 'gif',
            '': 'jpeg',
          }[ext] ??
          'jpeg';

      request.files.add(
        await http.MultipartFile.fromPath(
          'images[]',
          image.path,
          contentType: MediaType('image', mimeType),
        ),
      );
    }

    var streamedResponse = await request.send();
    var response = await http.Response.fromStream(streamedResponse);

    if (response.statusCode == 200 || response.statusCode == 201) {
      return json.decode(response.body);
    } else {
      throw Exception(
        'Failed to upload images. Status: ${response.statusCode}, Body: ${response.body}',
      );
    }
  }
}
