import 'package:flutter_test/flutter_test.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/models/local_message.dart';
import 'package:mr_garments_mobile/services/hive_chat_service.dart';
import 'package:mr_garments_mobile/services/connectivity_service.dart';

void main() {
  group('Hybrid Chat Integration Tests', () {
    test('should convert Message to LocalMessage and back correctly', () {
      // Create a test message
      final originalMessage = Message(
        id: 'test_message_123',
        senderId: 'user_456',
        senderName: 'Test User',
        type: MessageType.text,
        text: 'Hello, this is a test message!',
        timestamp: DateTime.now(),
        status: MessageStatus.sent,
      );

      // Convert to LocalMessage
      final localMessage = LocalMessage.fromMessage(originalMessage, 'chat_789');

      // Verify conversion
      expect(localMessage.id, originalMessage.id);
      expect(localMessage.senderId, originalMessage.senderId);
      expect(localMessage.senderName, originalMessage.senderName);
      expect(localMessage.text, originalMessage.text);
      expect(localMessage.chatId, 'chat_789');
      expect(localMessage.messageType, MessageType.text.index);
      expect(localMessage.messageStatus, MessageStatus.sent.index);

      // Convert back to Message
      final convertedMessage = localMessage.toMessage();

      // Verify round-trip conversion
      expect(convertedMessage.id, originalMessage.id);
      expect(convertedMessage.senderId, originalMessage.senderId);
      expect(convertedMessage.senderName, originalMessage.senderName);
      expect(convertedMessage.text, originalMessage.text);
      expect(convertedMessage.type, originalMessage.type);
      expect(convertedMessage.status, originalMessage.status);
    });

    test('should handle image messages with local paths', () {
      final imageMessage = Message(
        id: 'image_msg_123',
        senderId: 'user_456',
        senderName: 'Image Sender',
        type: MessageType.image,
        mediaUrl: 'https://example.com/image.jpg',
        localImagePath: '/Android/media/com.mrgarments/media/Images/Sent/image.jpg',
        localThumbnailPath: '/Android/media/com.mrgarments/media/Images/Sent/thumb.jpg',
        timestamp: DateTime.now(),
        status: MessageStatus.sent,
      );

      final localMessage = LocalMessage.fromMessage(imageMessage, 'chat_789');
      final convertedMessage = localMessage.toMessage();

      expect(convertedMessage.type, MessageType.image);
      expect(convertedMessage.mediaUrl, 'https://example.com/image.jpg');
      expect(convertedMessage.localImagePath, '/Android/media/com.mrgarments/media/Images/Sent/image.jpg');
      expect(convertedMessage.localThumbnailPath, '/Android/media/com.mrgarments/media/Images/Sent/thumb.jpg');
    });

    test('should handle connectivity service initialization', () {
      // Test that ConnectivityService can be instantiated
      final connectivityService = ConnectivityService.instance;
      expect(connectivityService, isNotNull);
      
      // Test that it has the expected properties
      expect(connectivityService.isConnected, isA<bool>());
      expect(connectivityService.connectionStatusStream, isNotNull);
    });

    test('should validate HiveChatService methods exist', () {
      // Test that HiveChatService has the expected static methods
      expect(HiveChatService.storeMessage, isA<Function>());
      expect(HiveChatService.getMessagesForChat, isA<Function>());
      expect(HiveChatService.getDatabaseStats, isA<Function>());
    });
  });
}
