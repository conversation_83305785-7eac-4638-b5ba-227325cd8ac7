import 'dart:io';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';

/// Custom gallery picker that ensures device gallery opens instead of Google Photos
class CustomGalleryPicker {
  static const MethodChannel _channel = MethodChannel('custom_gallery_picker');

  /// Pick multiple images from device gallery (not Google Photos)
  static Future<List<XFile>?> pickMultipleImagesFromGallery({
    int imageQuality = 80,
    int maxImages = 100,
  }) async {
    try {
      // First try to use native Android intent to open device gallery
      if (Platform.isAndroid) {
        final List<dynamic>? result = await _channel.invokeMethod(
          'pickMultipleImagesFromGallery',
          {'imageQuality': imageQuality, 'maxImages': maxImages},
        );

        if (result != null) {
          return result.map((path) => XFile(path.toString())).toList();
        }
      }

      // Fallback to regular image picker
      final ImagePicker picker = ImagePicker();
      return await picker.pickMultiImage(
        imageQuality: imageQuality,
        maxWidth: 1920,
        maxHeight: 1920,
      );
    } catch (e) {
      // If custom method fails, fallback to regular image picker
      final ImagePicker picker = ImagePicker();
      return await picker.pickMultiImage(
        imageQuality: imageQuality,
        maxWidth: 1920,
        maxHeight: 1920,
      );
    }
  }

  /// Pick single image from device gallery (not Google Photos)
  static Future<XFile?> pickImageFromGallery({
    int imageQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      // First try to use native Android intent to open device gallery
      if (Platform.isAndroid) {
        final String? result = await _channel.invokeMethod(
          'pickImageFromGallery',
          {
            'imageQuality': imageQuality,
            'maxWidth': maxWidth,
            'maxHeight': maxHeight,
          },
        );

        if (result != null) {
          return XFile(result);
        }
      }

      // Fallback to regular image picker
      final ImagePicker picker = ImagePicker();
      return await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: imageQuality,
        maxWidth: maxWidth?.toDouble(),
        maxHeight: maxHeight?.toDouble(),
      );
    } catch (e) {
      // If custom method fails, fallback to regular image picker
      final ImagePicker picker = ImagePicker();
      return await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: imageQuality,
        maxWidth: maxWidth?.toDouble(),
        maxHeight: maxHeight?.toDouble(),
      );
    }
  }
}



