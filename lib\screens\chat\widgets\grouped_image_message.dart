import 'dart:io';
import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/utils/image_utils.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_utils.dart';

import 'package:mr_garments_mobile/screens/chat/widgets/image_viewer_screen.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/enhanced_image_viewer_screen.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/image_forward_dialog.dart';
import 'package:mr_garments_mobile/services/receiver_download_service.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/models/message.dart';

class GroupedImageMessage extends StatelessWidget {
  final List<String> imageUrls;
  final List<Message> imageMessages; // The actual message objects
  final String senderName;
  final String timestamp;
  final bool isMe;
  final String chatId;
  final VoidCallback? onLongPress;

  const GroupedImageMessage({
    super.key,
    required this.imageUrls,
    required this.imageMessages,
    required this.senderName,
    required this.timestamp,
    required this.isMe,
    required this.chatId,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    // Determine the layout based on number of images
    if (imageUrls.isEmpty) return const SizedBox();
    if (imageUrls.length == 1) {
      return _buildSingleImage(context);
    }
    return _buildGridView(context);
  }

  Widget _buildSingleImage(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    // WhatsApp-like sizing: smaller max width and more reasonable constraints
    final maxWidth = screenWidth * 0.5; // Reduced from 0.6 to 0.5
    const minWidth = 140.0; // Minimum width to prevent too small images
    const maxHeight = 220.0; // Reduced from 250 to 200
    const minHeight = 140.0; // Minimum height

    return GestureDetector(
      onTap: () => _handleImageTap(context, 0),
      onLongPress: () => _showImageOptions(context),
      child: Container(
        constraints: BoxConstraints(
          minWidth: minWidth,
          maxWidth: maxWidth,
          minHeight: minHeight,
          maxHeight: maxHeight,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: AspectRatio(
            // Use a more WhatsApp-like aspect ratio for single images
            aspectRatio: 4 / 3, // 4:3 aspect ratio, similar to WhatsApp
            child: _buildImageWithStatus(
              imageUrls[0],
              imageMessages[0],
              width: maxWidth,
              height: maxHeight,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.circular(12),
              context: context,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGridView(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final displayCount = imageUrls.length > 4 ? 4 : imageUrls.length;
    final remainingCount = imageUrls.length - displayCount;

    return GestureDetector(
      onTap: () => _handleImageTap(context, 0),
      onLongPress: () => _showImageOptions(context),
      child: Container(
        width: screenWidth * 0.55, // Reduced from 0.6 to 0.55
        height: 220, // Reduced from 250 to 220
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
        child: LayoutBuilder(
          builder: (context, constraints) {
            if (imageUrls.length == 2) {
              return _buildTwoImageLayout(constraints, context);
            } else if (imageUrls.length == 3) {
              return _buildThreeImageLayout(constraints, context);
            } else {
              return _buildFourPlusImageLayout(
                constraints,
                remainingCount,
                context,
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildTwoImageLayout(
    BoxConstraints constraints,
    BuildContext context,
  ) {
    return Row(
      children: [
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(right: 1),
            child: ClipRRect(
              borderRadius: const BorderRadius.horizontal(
                left: Radius.circular(12),
              ),
              child: _buildImageWithStatus(
                imageUrls[0],
                imageMessages[0],
                width: constraints.maxWidth / 2,
                height: constraints.maxHeight,
                fit: BoxFit.cover,
                context: context,
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(left: 1),
            child: ClipRRect(
              borderRadius: const BorderRadius.horizontal(
                right: Radius.circular(12),
              ),
              child: _buildImageWithStatus(
                imageUrls[1],
                imageMessages[1],
                width: constraints.maxWidth / 2,
                height: constraints.maxHeight,
                fit: BoxFit.cover,
                context: context,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildThreeImageLayout(
    BoxConstraints constraints,
    BuildContext context,
  ) {
    return Row(
      children: [
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(right: 1),
            child: ClipRRect(
              borderRadius: const BorderRadius.horizontal(
                left: Radius.circular(12),
              ),
              child: _buildImageWithStatus(
                imageUrls[0],
                imageMessages[0],
                width: constraints.maxWidth / 2,
                height: constraints.maxHeight,
                fit: BoxFit.cover,
                context: context,
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(left: 1),
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 1),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(12),
                      ),
                      child: _buildImageWithStatus(
                        imageUrls[1],
                        imageMessages[1],
                        width: constraints.maxWidth / 2,
                        height: constraints.maxHeight / 2,
                        fit: BoxFit.cover,
                        context: context,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(top: 1),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        bottomRight: Radius.circular(12),
                      ),
                      child: _buildImageWithStatus(
                        imageUrls[2],
                        imageMessages[2],
                        width: constraints.maxWidth / 2,
                        height: constraints.maxHeight / 2,
                        fit: BoxFit.cover,
                        context: context,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFourPlusImageLayout(
    BoxConstraints constraints,
    int remainingCount,
    BuildContext context,
  ) {
    return Column(
      children: [
        Expanded(
          child: Row(
            children: [
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(right: 1, bottom: 1),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                    ),
                    child: _buildImageWithStatus(
                      imageUrls[0],
                      imageMessages[0],
                      width: constraints.maxWidth / 2,
                      height: constraints.maxHeight / 2,
                      fit: BoxFit.cover,
                      context: context,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(left: 1, bottom: 1),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(12),
                    ),
                    child: _buildImageWithStatus(
                      imageUrls[1],
                      imageMessages[1],
                      width: constraints.maxWidth / 2,
                      height: constraints.maxHeight / 2,
                      fit: BoxFit.cover,
                      context: context,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(right: 1, top: 1),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                    ),
                    child: _buildImageWithStatus(
                      imageUrls[2],
                      imageMessages[2],
                      width: constraints.maxWidth / 2,
                      height: constraints.maxHeight / 2,
                      fit: BoxFit.cover,
                      context: context,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(left: 1, top: 1),
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      ClipRRect(
                        borderRadius: const BorderRadius.only(
                          bottomRight: Radius.circular(12),
                        ),
                        child: _buildImageWithStatus(
                          imageUrls[3],
                          imageMessages[3],
                          width: constraints.maxWidth / 2,
                          height: constraints.maxHeight / 2,
                          fit: BoxFit.cover,
                          context: context,
                        ),
                      ),
                      if (remainingCount > 0)
                        ClipRRect(
                          borderRadius: const BorderRadius.only(
                            bottomRight: Radius.circular(12),
                          ),
                          child: Container(
                            color: Colors.black.withValues(alpha: 0.5),
                            child: Center(
                              child: Text(
                                '+$remainingCount',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Handle image tap - show images from local storage if available, or make them visible
  Future<void> _handleImageTap(BuildContext context, int initialIndex) async {
    if (!isMe) {
      // For receiver, check if images are ready for display
      final targetMessage = imageMessages[initialIndex];
      final isReadyForDisplay =
          targetMessage.metadata?['readyForDisplay'] == true;
      final isAutoDownloaded =
          targetMessage.metadata?['autoDownloaded'] == true;

      debugPrint('🔍 Handle tap - Message: ${targetMessage.id}');
      debugPrint('🔍 Handle tap - Ready for display: $isReadyForDisplay');
      debugPrint('🔍 Handle tap - Auto downloaded: $isAutoDownloaded');

      if (isAutoDownloaded && !isReadyForDisplay) {
        // Images are downloaded to local storage but not visible yet
        // Make them visible in chat
        await _makeImagesVisibleFromLocalStorage(context);
        return;
      } else if (!isAutoDownloaded) {
        // Images not downloaded yet, show download dialog
        await _downloadImageGroup(context);
        return;
      }
    }

    // Image is ready for viewing, open viewer
    _openImageViewer(context, initialIndex);
  }

  /// Make images visible from local storage (they're already downloaded)
  Future<void> _makeImagesVisibleFromLocalStorage(BuildContext context) async {
    try {
      debugPrint('🔍 Making images visible from local storage...');

      // Update all messages in the group to be ready for display
      for (final message in imageMessages) {
        if (message.metadata?['autoDownloaded'] == true) {
          // Import ChatService for the update
          await ChatService.updateMessageMetadata(
            chatId: chatId,
            messageId: message.id,
            metadata: {'readyForDisplay': true},
          );
          debugPrint('✅ Made image visible: ${message.id}');
        }
      }

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${imageMessages.length} image${imageMessages.length > 1 ? 's' : ''} are now visible',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Error making images visible: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error making images visible'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// Download all images in the group for receiver
  Future<void> _downloadImageGroup(BuildContext context) async {
    // Show confirmation dialog
    final shouldDownload = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Download Images'),
            content: Text(
              'Download ${imageMessages.length} image${imageMessages.length > 1 ? 's' : ''}?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF005368),
                ),
                child: const Text(
                  'Download',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );

    if (shouldDownload != true) return;

    // Show loading dialog
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Downloading images...'),
                ],
              ),
            ),
      );
    }

    // Download all images in the group
    int downloadedCount = 0;
    for (final message in imageMessages) {
      // Use the same detection logic as _hasReceiverLocalImage
      final hasDownloadedImage = _hasReceiverLocalImage(message);

      debugPrint('🔍 Download check - Message: ${message.id}');
      debugPrint('🔍 Download check - Has downloaded: $hasDownloadedImage');
      debugPrint('🔍 Download check - Media URL: ${message.mediaUrl != null}');

      if (!hasDownloadedImage && message.mediaUrl != null) {
        debugPrint('🔍 Starting download for message: ${message.id}');
        final success = await ReceiverDownloadService.downloadImageForReceiver(
          messageId: message.id,
          chatId: chatId,
          downloadUrl: message.mediaUrl!,
          senderId: message.senderId,
          onProgress: (progress) {
            // Progress callback for individual images
            // Could be used to show overall progress if needed
          },
        );
        if (success) {
          downloadedCount++;
          debugPrint('✅ Download completed for message: ${message.id}');
        } else {
          debugPrint('❌ Download failed for message: ${message.id}');
        }
      } else {
        debugPrint(
          '🔍 Skipping download - already exists or no URL: ${message.id}',
        );
      }
    }

    // Close loading dialog
    if (context.mounted) {
      Navigator.of(context).pop();
    }

    // Wait longer for Firestore to update and trigger stream rebuild
    await Future.delayed(const Duration(milliseconds: 1000));

    // Show success message
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '$downloadedCount image${downloadedCount > 1 ? 's' : ''} downloaded successfully',
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _openImageViewer(BuildContext context, int initialIndex) {
    // Use enhanced image viewer if chatId is available
    if (chatId.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => EnhancedImageViewerScreen(
                chatId: chatId,
                initialImageUrl: imageUrls[initialIndex],
                senderName: senderName,
                timestamp: timestamp,
                // Pass the specific group data for viewing all images in this group
                specificGroupUrls: imageUrls,
                specificGroupMessages: imageMessages,
              ),
        ),
      );
    } else {
      // Fallback to original viewer for grouped images only
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => ImageViewerScreen(
                imageUrls: imageUrls,
                imageMessages: imageMessages,
                initialIndex: 0,
                senderName: senderName,
                timestamp: timestamp,
                chatId: chatId,
                onImagesDeleted: (deletedIndices) {
                  // Handle image deletion - this will be handled by the parent chat screen
                  // The parent should refresh the message list
                },
              ),
        ),
      );
    }
  }

  void _showImageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Forward option
                ListTile(
                  leading: const Icon(Icons.forward, color: Color(0xFF005368)),
                  title: Text(
                    'Forward Images',
                    style: TextStyle(fontSize: 16, color: Colors.black87),
                  ),
                  subtitle: Text(
                    '${imageUrls.length} image${imageUrls.length > 1 ? 's' : ''}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showForwardDialog(context);
                  },
                ),

                // View all images option
                ListTile(
                  leading: const Icon(
                    Icons.photo_library,
                    color: Color(0xFF005368),
                  ),
                  title: const Text(
                    'View All Images',
                    style: TextStyle(fontSize: 16, color: Colors.black87),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _openImageViewer(context, 0);
                  },
                ),

                // Message options (if callback provided)
                if (onLongPress != null)
                  ListTile(
                    leading: const Icon(
                      Icons.more_horiz,
                      color: Color(0xFF005368),
                    ),
                    title: const Text(
                      'Select',
                      style: TextStyle(fontSize: 16, color: Colors.black87),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      onLongPress?.call();
                    },
                  ),

                // Bottom padding for safe area
                SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
              ],
            ),
          ),
    );
  }

  void _showForwardDialog(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ImageForwardScreen(
              imageUrls: imageUrls,
              imageMessages: imageMessages,
              fromChatId: chatId,
            ),
      ),
    );
  }

  /// Build image with status indicator for pending/failed messages
  /// For received messages, shows download option instead of auto-loading
  Widget _buildImageWithStatus(
    String imageUrl,
    Message message, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    BuildContext? context,
  }) {
    // Debug logging for forwarded messages
    if (message.isForwarded) {
      debugPrint('🔍 Building image for forwarded message: ${message.id}');
      debugPrint('🔍 Provided imageUrl: $imageUrl');
      debugPrint('🔍 Message mediaUrl: ${message.mediaUrl}');
      debugPrint('🔍 Message bestImagePath: ${message.bestImagePath}');
      debugPrint('🔍 Message localImagePath: ${message.localImagePath}');
    }
    // For received messages (not sent by current user), check if downloaded
    if (!isMe) {
      return _buildReceiverImageForGroup(
        imageUrl,
        message,
        width: width,
        height: height,
        fit: fit,
        borderRadius: borderRadius,
        context: context,
      );
    }

    // For sent messages, use the original logic
    // But for forwarded messages sent by current user, use network image to avoid local storage issues
    if (message.isForwarded) {
      debugPrint(
        '🔍 Forwarded message from sender: ${message.id}, using network image',
      );
      return _buildNetworkImage(
        imageUrl,
        message,
        width: width,
        height: height,
        fit: fit,
        borderRadius: borderRadius,
      );
    }

    // Check if this is a local file or optimistic message
    final isLocalFile = message.metadata?['isLocalFile'] == true;
    final hasLocalImage = message.hasLocalImage;
    final isOptimistic =
        message.isLocalOnly ||
        message.isUploading ||
        message.isPendingUpload ||
        message.isUploadFailed;

    if (isLocalFile || hasLocalImage || isOptimistic) {
      // For local/uploading images, use the best available image path
      final imagePath = message.bestImagePath ?? imageUrl;

      // Check if we have a valid image path
      if (imagePath.isEmpty) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: borderRadius,
          ),
          child: const Icon(Icons.broken_image, color: Colors.grey),
        );
      }

      Widget imageWidget = EnhancedImageUtils.buildChatImage(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        borderRadius: borderRadius,
        isLocalFile: hasLocalImage,
      );

      // Add upload status overlay
      if (message.isUploading) {
        imageWidget = Stack(
          children: [
            imageWidget,
            Positioned.fill(
              child: Container(
                color: Colors.black.withValues(alpha: 0.3),
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                      if (message.uploadProgress != null &&
                          message.uploadProgress! > 0)
                        Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: Text(
                            '${(message.uploadProgress! * 100).toInt()}%',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      } else if (message.isUploadFailed) {
        imageWidget = Stack(
          children: [
            imageWidget,
            Positioned.fill(
              child: Container(
                color: Colors.black.withValues(alpha: 0.5),
                child: const Center(
                  child: Icon(Icons.error_outline, color: Colors.red, size: 32),
                ),
              ),
            ),
          ],
        );
      }

      return imageWidget;
    } else {
      // Use regular image utils for network images
      return ImageUtils.buildChatImage(
        imageUrl,
        width: width ?? 200,
        height: height ?? 200,
        fit: fit,
        borderRadius: borderRadius ?? BorderRadius.zero,
      );
    }
  }

  /// Build receiver image for grouped layout with download functionality
  Widget _buildReceiverImageForGroup(
    String imageUrl,
    Message message, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    BuildContext? context,
  }) {
    // For forwarded messages, always use network image to avoid local storage issues
    if (message.isForwarded) {
      debugPrint(
        '🔍 Forwarded message detected: ${message.id}, using network image',
      );
      return _buildNetworkImage(
        imageUrl,
        message,
        width: width,
        height: height,
        fit: fit,
        borderRadius: borderRadius,
      );
    }

    // For receiver messages, check if image is downloaded in Received folder
    final hasLocalImage = _hasReceiverLocalImage(message);

    // Debug logging to track download status
    debugPrint('🔍 Receiver image check: ${message.id}');
    debugPrint('🔍 Has local image: $hasLocalImage');
    debugPrint('🔍 Local image path: ${message.localImagePath}');
    debugPrint(
      '🔍 Metadata hasLocalImage: ${message.metadata?['hasLocalImage']}',
    );

    if (hasLocalImage) {
      // Image is downloaded, show it normally
      // Determine the correct path to use
      String imagePath = message.localImagePath!;

      // If the message still has Sent folder path but image exists in Received folder,
      // find the actual downloaded image in Received folder using URL-based mapping
      if (message.localImagePath!.contains('/Sent/') &&
          message.metadata?['hasLocalImage'] == true) {
        // Try to find the correct downloaded image using URL hash mapping
        final downloadedImagePath = _findDownloadedImageForMessage(message);
        if (downloadedImagePath != null) {
          imagePath = downloadedImagePath;
          debugPrint('🔍 Using mapped received image for display: $imagePath');
        }
      }

      return EnhancedImageUtils.buildChatImage(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        borderRadius: borderRadius,
        isLocalFile: true,
      );
    } else {
      // Image not downloaded, show download overlay
      return Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: borderRadius,
        ),
        child: Stack(
          children: [
            // Show thumbnail if available
            if (message.thumbnailUrl != null)
              EnhancedImageUtils.buildChatImage(
                message.thumbnailUrl!,
                width: width,
                height: height,
                fit: fit,
                borderRadius: borderRadius,
              )
            else
              Container(
                width: width,
                height: height,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: borderRadius,
                ),
                child: const Center(
                  child: Icon(Icons.image, size: 48, color: Colors.grey),
                ),
              ),
          ],
        ),
      );
    }
  }

  /// Find the downloaded image file for a specific message using message ID mapping
  String? _findDownloadedImageForMessage(Message message) {
    try {
      final receivedDir = Directory(
        '/storage/emulated/0/Android/media/com.mrgarments/media/Images/Received',
      );

      if (!receivedDir.existsSync()) return null;

      final files = receivedDir.listSync();
      final imageFiles =
          files
              .where(
                (file) =>
                    file.path.endsWith('.jpg') ||
                    file.path.endsWith('.jpeg') ||
                    file.path.endsWith('.png'),
              )
              .toList();

      if (imageFiles.isEmpty) return null;

      // Strategy 1: Check if there's a mapping stored in message metadata
      final downloadedFilename = message.metadata?['downloadedFilename'];
      if (downloadedFilename != null) {
        final mappedPath =
            '/storage/emulated/0/Android/media/com.mrgarments/media/Images/Received/$downloadedFilename';
        if (File(mappedPath).existsSync()) {
          debugPrint('🔍 Found mapped image: $mappedPath');
          return mappedPath;
        }
      }

      // Strategy 2: Try to find image by message ID or URL hash
      final messageId = message.id;
      final mediaUrl = message.mediaUrl ?? '';

      // Look for files that might contain the message ID or URL hash
      for (final file in imageFiles) {
        final filename = file.path.split('/').last;

        // Check if filename contains message ID
        if (filename.contains(messageId)) {
          debugPrint('🔍 Found image by message ID: ${file.path}');
          return file.path;
        }

        // Check if filename contains URL hash (if available)
        if (mediaUrl.isNotEmpty) {
          final urlHash = mediaUrl.hashCode.abs().toString();
          if (filename.contains(urlHash)) {
            debugPrint('🔍 Found image by URL hash: ${file.path}');
            return file.path;
          }
        }
      }

      // Strategy 3: Use file modification time and message timestamp correlation
      // Find the image file with modification time closest to message timestamp
      if (imageFiles.isNotEmpty) {
        final messageTimestamp = message.timestamp.millisecondsSinceEpoch;

        // Sort files by how close their modification time is to message timestamp
        imageFiles.sort((a, b) {
          final aFile = File(a.path);
          final bFile = File(b.path);

          try {
            final aModTime = aFile.lastModifiedSync().millisecondsSinceEpoch;
            final bModTime = bFile.lastModifiedSync().millisecondsSinceEpoch;

            final aDiff = (aModTime - messageTimestamp).abs();
            final bDiff = (bModTime - messageTimestamp).abs();

            return aDiff.compareTo(bDiff);
          } catch (e) {
            return 0;
          }
        });

        // Use the file with the closest modification time
        final closestFile = imageFiles.first;
        final closestFileTime =
            File(closestFile.path).lastModifiedSync().millisecondsSinceEpoch;
        final timeDifference = (closestFileTime - messageTimestamp).abs();

        // Only use this file if the time difference is reasonable (within 5 minutes)
        if (timeDifference <= 5 * 60 * 1000) {
          debugPrint(
            '🔍 Using time-based mapping (time diff: ${timeDifference}ms): ${closestFile.path}',
          );
          return closestFile.path;
        }
      }

      // Strategy 4: Fallback - if message has localImagePath, try to find corresponding received image
      if (message.localImagePath != null &&
          message.localImagePath!.isNotEmpty) {
        final originalFilename = message.localImagePath!.split('/').last;

        // Look for a file with the same name in received folder
        for (final file in imageFiles) {
          final receivedFilename = file.path.split('/').last;
          if (receivedFilename == originalFilename) {
            debugPrint('🔍 Found image by filename match: ${file.path}');
            return file.path;
          }
        }
      }

      debugPrint('🔍 No suitable image found for message: ${message.id}');
    } catch (e) {
      debugPrint('🔍 Error finding downloaded image for message: $e');
    }

    return null;
  }

  /// Build network image widget for forwarded messages
  Widget _buildNetworkImage(
    String imageUrl,
    Message message, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    // Use the message's mediaUrl if available, otherwise use the provided imageUrl
    // For forwarded messages, prioritize mediaUrl over the provided imageUrl
    final networkUrl =
        message.mediaUrl?.isNotEmpty == true
            ? message.mediaUrl!
            : (imageUrl.isNotEmpty ? imageUrl : message.mediaUrl ?? '');

    debugPrint('🔍 Network image URL selection for ${message.id}:');
    debugPrint('🔍   - message.mediaUrl: ${message.mediaUrl}');
    debugPrint('🔍   - provided imageUrl: $imageUrl');
    debugPrint('🔍   - selected networkUrl: $networkUrl');

    if (networkUrl.isEmpty) {
      debugPrint('🚨 EMPTY NETWORK URL for forwarded message ${message.id}');
      debugPrint('🚨   - message.mediaUrl: "${message.mediaUrl}"');
      debugPrint('🚨   - provided imageUrl: "$imageUrl"');
      debugPrint('🚨   - message.type: ${message.type}');
      debugPrint('🚨   - message.isForwarded: ${message.isForwarded}');
      return Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: borderRadius,
        ),
        child: const Icon(Icons.broken_image, color: Colors.grey),
      );
    }

    return ImageUtils.buildChatImage(
      networkUrl,
      width: width ?? 100,
      height: height ?? 100,
      fit: fit,
      borderRadius: borderRadius,
    );
  }

  /// Check if receiver message has local image ready for display
  bool _hasReceiverLocalImage(Message message) {
    // New flow: Check if image is ready for display (auto-downloaded and made visible)
    final isReadyForDisplay = message.metadata?['readyForDisplay'] == true;
    final isAutoDownloaded = message.metadata?['autoDownloaded'] == true;

    debugPrint('🔍 Receiver image check for ${message.id}:');
    debugPrint('  - readyForDisplay: $isReadyForDisplay');
    debugPrint('  - autoDownloaded: $isAutoDownloaded');

    // If ready for display, check if file actually exists
    if (isReadyForDisplay) {
      // First check if the message has been updated with a Received folder path
      if (message.localImagePath != null &&
          message.localImagePath!.isNotEmpty &&
          message.localImagePath!.contains('/Received/')) {
        try {
          final file = File(message.localImagePath!);
          final exists = file.existsSync();
          debugPrint(
            '🔍 Direct path check: $exists at ${message.localImagePath}',
          );
          return exists;
        } catch (e) {
          debugPrint('🔍 Error checking direct path: $e');
          return false;
        }
      }

      // Try to find the downloaded image
      final downloadedImagePath = _findDownloadedImageForMessage(message);
      if (downloadedImagePath != null) {
        final file = File(downloadedImagePath);
        final exists = file.existsSync();
        debugPrint('🔍 Specific image exists: $exists at $downloadedImagePath');
        return exists;
      }
    }

    // If not ready for display or file doesn't exist, return false
    debugPrint('🔍 Image not ready for display for message ${message.id}');
    return false;
  }
}
