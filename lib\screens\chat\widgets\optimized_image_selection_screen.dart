import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/services/optimized_image_service.dart';

class OptimizedImageSelectionScreen extends StatefulWidget {
  final List<XFile>? initialImages;
  final Function(List<ProcessedImage>) onImagesSelected;
  final int maxImages;

  const OptimizedImageSelectionScreen({
    super.key,
    this.initialImages,
    required this.onImagesSelected,
    this.maxImages = 100,
  });

  @override
  State<OptimizedImageSelectionScreen> createState() => _OptimizedImageSelectionScreenState();
}

class _OptimizedImageSelectionScreenState extends State<OptimizedImageSelectionScreen>
    with TickerProviderStateMixin {
  ImageProcessingState _processingState = const ImageProcessingState();
  late AnimationController _progressAnimationController;
  late AnimationController _fadeAnimationController;
  late Animation<double> _progressAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _progressAnimationController, curve: Curves.easeInOut),
    );
    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _fadeAnimationController, curve: Curves.easeInOut),
    );

    // Process initial images if provided
    if (widget.initialImages != null && widget.initialImages!.isNotEmpty) {
      _processImages(widget.initialImages!);
    } else {
      _pickImages();
    }
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    _fadeAnimationController.dispose();
    super.dispose();
  }


  Future<void> _pickImages() async {
    setState(() {
      _processingState = _processingState.copyWith(
        status: ImageProcessingStatus.picking,
      );
    });

    try {
      final images = await OptimizedImageService.pickMultipleImages(
        maxImages: widget.maxImages,
      );

      if (images != null && images.isNotEmpty) {
        await _processImages(images);
      } else {
        // User cancelled or no images selected
        Navigator.pop(context);
      }
    } catch (e) {
      setState(() {
        _processingState = _processingState.copyWith(
          status: ImageProcessingStatus.error,
          errorMessage: 'Failed to pick images: $e',
        );
      });
    }
  }

  Future<void> _processImages(List<XFile> images) async {
    setState(() {
      _processingState = _processingState.copyWith(
        status: ImageProcessingStatus.validating,
        totalCount: images.length,
      );
    });

    _progressAnimationController.forward();

    try {
      // Process images with progress updates
      final processedImages = await OptimizedImageService.batchProcessImages(
        images,
        onProgress: (processed, total) {
          if (mounted) {
            setState(() {
              _processingState = _processingState.copyWith(
                status: ImageProcessingStatus.compressing,
                processedCount: processed,
                totalCount: total,
              );
            });
          }
        },
      );

      if (processedImages.isNotEmpty) {
        setState(() {
          _processingState = _processingState.copyWith(
            status: ImageProcessingStatus.ready,
            processedImages: processedImages,
            processedCount: processedImages.length,
          );
        });
        _fadeAnimationController.forward();
      } else {
        setState(() {
          _processingState = _processingState.copyWith(
            status: ImageProcessingStatus.error,
            errorMessage: 'No valid images found',
          );
        });
      }
    } catch (e) {
      setState(() {
        _processingState = _processingState.copyWith(
          status: ImageProcessingStatus.error,
          errorMessage: 'Failed to process images: $e',
        );
      });
    }
  }

  Future<void> _pickMoreImages() async {
    try {
      final newImages = await OptimizedImageService.pickMultipleImages(
        maxImages: widget.maxImages - _processingState.processedImages.length,
      );

      if (newImages != null && newImages.isNotEmpty) {
        // Combine with existing images
        final allImages = [
          ..._processingState.processedImages.map((p) => XFile(p.originalPath)),
          ...newImages,
        ];
        
        await _processImages(allImages);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking more images: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    final updatedImages = List<ProcessedImage>.from(_processingState.processedImages);
    updatedImages.removeAt(index);
    
    setState(() {
      _processingState = _processingState.copyWith(
        processedImages: updatedImages,
        processedCount: updatedImages.length,
      );
    });
  }

  void _sendImages() {
    if (_processingState.processedImages.isNotEmpty) {
      setState(() {
        _processingState = _processingState.copyWith(
          status: ImageProcessingStatus.sending,
        );
      });
      
      widget.onImagesSelected(_processingState.processedImages);
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column( 
          children: [
            _buildAppBar(),
            if (_processingState.isProcessing) _buildProcessingIndicator(),
            if (_processingState.hasError) _buildErrorState(),
            if (_processingState.isReady) _buildImageGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getAppBarTitle(),
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (_processingState.isReady && _processingState.processedImages.isNotEmpty)
                  Text(
                    _getTotalSizeText(),
                    style: GoogleFonts.poppins(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ),
          if (_processingState.isReady) ...[
            if (_processingState.processedImages.length < widget.maxImages)
              IconButton(
                icon: const Icon(LucideIcons.plus, color: Colors.white),
                onPressed: _pickMoreImages,
              ),
            IconButton(
              icon: const Icon(Icons.send, color: Colors.white),
              onPressed: _processingState.processedImages.isNotEmpty ? _sendImages : null,
            ),
          ],
        ],
      ),
    );
  }

  String _getAppBarTitle() {
    switch (_processingState.status) {
      case ImageProcessingStatus.picking:
        return 'Selecting images...';
      case ImageProcessingStatus.validating:
        return 'Validating images...';
      case ImageProcessingStatus.compressing:
        return 'Processing ${_processingState.processedCount}/${_processingState.totalCount}';
      case ImageProcessingStatus.ready:
        return '${_processingState.processedImages.length} selected';
      case ImageProcessingStatus.sending:
        return 'Sending images...';
      case ImageProcessingStatus.error:
        return 'Error';
      default:
        return 'Select Images';
    }
  }

  String _getTotalSizeText() {
    final totalSize = _processingState.processedImages
        .fold<int>(0, (sum, image) => sum + image.fileSize);
    return 'Total: ${OptimizedImageService.formatFileSize(totalSize)}';
  }

  Widget _buildProcessingIndicator() {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, child) {
                return Container(
                  width: 120,
                  height: 120,
                  child: Stack(
                    children: [
                      // Background circle
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withOpacity(0.1),
                        ),
                      ),
                      // Progress circle
                      if (_processingState.totalCount > 0)
                        SizedBox(
                          width: 120,
                          height: 120,
                          child: CircularProgressIndicator(
                            value: _processingState.progress,
                            strokeWidth: 6,
                            valueColor: const AlwaysStoppedAnimation<Color>(
                              Color(0xFF25D366), // WhatsApp green
                            ),
                            backgroundColor: Colors.white.withOpacity(0.2),
                          ),
                        ),
                      // Center icon
                      Center(
                        child: Icon(
                          _getProcessingIcon(),
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(height: 24),
            Text(
              _getProcessingText(),
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            if (_processingState.totalCount > 0)
              Text(
                '${_processingState.processedCount} of ${_processingState.totalCount} images',
                style: GoogleFonts.poppins(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
          ],
        ),
      ),
    );
  }

  IconData _getProcessingIcon() {
    switch (_processingState.status) {
      case ImageProcessingStatus.picking:
        return LucideIcons.image;
      case ImageProcessingStatus.validating:
        return LucideIcons.checkCircle;
      case ImageProcessingStatus.compressing:
        return LucideIcons.zap;
      default:
        return LucideIcons.image;
    }
  }

  String _getProcessingText() {
    switch (_processingState.status) {
      case ImageProcessingStatus.picking:
        return 'Selecting images...';
      case ImageProcessingStatus.validating:
        return 'Validating images...';
      case ImageProcessingStatus.compressing:
        return 'Optimizing for sharing...';
      default:
        return 'Processing...';
    }
  }

  Widget _buildErrorState() {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.alertCircle,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _processingState.errorMessage ?? 'Something went wrong',
              style: GoogleFonts.poppins(
                color: Colors.white70,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _pickImages,
              icon: const Icon(LucideIcons.refreshCw),
              label: Text(
                'Try Again',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF25D366),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGrid() {
    return Expanded(
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: GridView.builder(
          padding: const EdgeInsets.all(4),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
          ),
          itemCount: _processingState.processedImages.length,
          itemBuilder: (context, index) {
            final processedImage = _processingState.processedImages[index];
            return _buildImageTile(processedImage, index);
          },
        ),
      ),
    );
  }

  Widget _buildImageTile(ProcessedImage processedImage, int index) {
    return Stack(
      fit: StackFit.expand,
      children: [
        // Image
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: processedImage.thumbnail != null
              ? Image.memory(
                  processedImage.thumbnail!,
                  fit: BoxFit.cover,
                )
              : Image.file(
                  processedImage.compressedFile,
                  fit: BoxFit.cover,
                ),
        ),
        
        // Gradient overlay for better text visibility
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Colors.black.withOpacity(0.3),
              ],
            ),
          ),
        ),
        
        // Remove button
        Positioned(
          top: 8,
          right: 8,
          child: GestureDetector(
            onTap: () => _removeImage(index),
            child: Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ),
        
        // File size indicator
        Positioned(
          bottom: 8,
          left: 8,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              processedImage.formattedSize,
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        
        // Index indicator
        Positioned(
          top: 8,
          left: 8,
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: const Color(0xFF25D366),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
            child: Center(
              child: Text(
                '${index + 1}',
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}