import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:mr_garments_mobile/models/local_message.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

/// Hive-based local database service for offline chat functionality
class HiveChatService {
  static const String _messagesBoxName = 'chat_messages';
  static const String _chatsBoxName = 'chats_metadata';
  static const String _syncQueueBoxName = 'sync_queue';

  static Box<LocalMessage>? _messagesBox;
  static Box<Map>? _chatsBox;
  static Box<Map>? _syncQueueBox;
  static bool _initialized = false;

  /// Initialize Hive database
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Initialize Hive
      await Hive.initFlutter();

      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(LocalMessageAdapter());
      }

      // Open boxes
      _messagesBox = await Hive.openBox<LocalMessage>(_messagesBoxName);
      _chatsBox = await Hive.openBox<Map>(_chatsBoxName);
      _syncQueueBox = await Hive.openBox<Map>(_syncQueueBoxName);

      _initialized = true;
      debugPrint('✅ HiveChatService initialized successfully');
      debugPrint('📊 Messages in DB: ${_messagesBox?.length ?? 0}');
    } catch (e) {
      debugPrint('❌ Error initializing HiveChatService: $e');
      rethrow;
    }
  }

  /// Ensure service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
  }

  /// Store a message locally
  static Future<bool> storeMessage(LocalMessage message) async {
    try {
      await _ensureInitialized();

      // Use message ID as key for easy retrieval
      await _messagesBox!.put(message.id, message);

      // Update chat metadata
      await _updateChatMetadata(message.chatId, message);

      debugPrint('💾 Stored message locally: ${message.id}');
      return true;
    } catch (e) {
      debugPrint('❌ Error storing message: $e');
      return false;
    }
  }

  /// Store multiple messages locally (batch operation)
  static Future<bool> storeMessages(List<LocalMessage> messages) async {
    try {
      await _ensureInitialized();

      final Map<String, LocalMessage> messageMap = {};
      for (final message in messages) {
        messageMap[message.id] = message;
      }

      await _messagesBox!.putAll(messageMap);

      // Update chat metadata for each unique chat
      final Set<String> chatIds = messages.map((m) => m.chatId).toSet();
      for (final chatId in chatIds) {
        final latestMessage = messages
            .where((m) => m.chatId == chatId)
            .reduce((a, b) => a.timestamp > b.timestamp ? a : b);
        await _updateChatMetadata(chatId, latestMessage);
      }

      debugPrint('💾 Stored ${messages.length} messages locally');
      return true;
    } catch (e) {
      debugPrint('❌ Error storing messages: $e');
      return false;
    }
  }

  /// Get messages for a specific chat
  static Future<List<LocalMessage>> getMessagesForChat(
    String chatId, {
    int limit = 50,
    int? beforeTimestamp,
  }) async {
    try {
      await _ensureInitialized();

      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) return [];

      // Get all messages for the chat
      final allMessages =
          _messagesBox!.values
              .where(
                (message) =>
                    message.chatId == chatId &&
                    !message.deletedBy.contains(currentUserId.toString()),
              )
              .toList();

      // Sort by timestamp (newest first)
      allMessages.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // Apply before timestamp filter if provided
      List<LocalMessage> filteredMessages = allMessages;
      if (beforeTimestamp != null) {
        filteredMessages =
            allMessages.where((m) => m.timestamp < beforeTimestamp).toList();
      }

      // Apply limit
      final limitedMessages = filteredMessages.take(limit).toList();

      debugPrint(
        '📱 Retrieved ${limitedMessages.length} messages for chat $chatId',
      );
      return limitedMessages;
    } catch (e) {
      debugPrint('❌ Error getting messages for chat: $e');
      return [];
    }
  }

  /// Get a specific message by ID
  static Future<LocalMessage?> getMessage(String messageId) async {
    try {
      await _ensureInitialized();
      return _messagesBox!.get(messageId);
    } catch (e) {
      debugPrint('❌ Error getting message: $e');
      return null;
    }
  }

  /// Update a message
  static Future<bool> updateMessage(LocalMessage message) async {
    try {
      await _ensureInitialized();

      final updatedMessage = message.copyWith(
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      await _messagesBox!.put(message.id, updatedMessage);
      debugPrint('🔄 Updated message: ${message.id}');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating message: $e');
      return false;
    }
  }

  /// Update only the local file paths for a message (receiver/sender stored images)
  static Future<bool> updateLocalPaths(
    String messageId, {
    String? localPath,
    String? localThumbnailPath,
  }) async {
    try {
      await _ensureInitialized();

      final existing = await getMessage(messageId);
      if (existing == null) return false;

      final updated = existing.copyWith(
        localPath: localPath ?? existing.localPath,
        localThumbnailPath: localThumbnailPath ?? existing.localThumbnailPath,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      await _messagesBox!.put(messageId, updated);
      debugPrint('🖼️ Updated local paths for message: $messageId');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating local paths for $messageId: $e');
      return false;
    }
  }

  /// Delete a message for a specific user
  static Future<bool> deleteMessageForUser(
    String messageId,
    String userId,
  ) async {
    try {
      await _ensureInitialized();

      final message = await getMessage(messageId);
      if (message == null) return false;

      final updatedDeletedBy = List<String>.from(message.deletedBy);
      if (!updatedDeletedBy.contains(userId)) {
        updatedDeletedBy.add(userId);
      }

      final updatedMessage = message.copyWith(deletedBy: updatedDeletedBy);
      return await updateMessage(updatedMessage);
    } catch (e) {
      debugPrint('❌ Error deleting message for user: $e');
      return false;
    }
  }

  /// Completely delete a message from local storage (admin deletion)
  static Future<bool> deleteMessage(String messageId) async {
    try {
      await _ensureInitialized();

      final message = await getMessage(messageId);
      if (message == null) return false;

      await _messagesBox!.delete(messageId);
      debugPrint('🗑 Deleted message from local storage: $messageId');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting message: $e');

      return false;
    }
  }   

  /// Get unsent messages (for sync when online)
  static Future<List<LocalMessage>> getUnsentMessages() async {
    try {
      await _ensureInitialized();

      final unsentMessages =
          _messagesBox!.values
              .where((message) => !message.isSent && message.isLocalOnly)
              .toList();

      // Sort by timestamp (oldest first for sending)
      unsentMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      debugPrint('📤 Found ${unsentMessages.length} unsent messages');
      return unsentMessages;
    } catch (e) {
      debugPrint('❌ Error getting unsent messages: $e');
      return [];
    }
  }

  /// Mark message as sent
  static Future<bool> markMessageAsSent(String messageId) async {
    try {
      await _ensureInitialized();

      final message = await getMessage(messageId);
      if (message == null) return false;

      final updatedMessage = message.copyWith(
        isSent: true,
        isLocalOnly: false,
        messageStatus: MessageStatus.sent.index,
      );

      return await updateMessage(updatedMessage);
    } catch (e) {
      debugPrint('❌ Error marking message as sent: $e');
      return false;
    }
  }

  /// Add message to sync queue
  static Future<bool> addToSyncQueue(String messageId, String action) async {
    try {
      await _ensureInitialized();

      final syncItem = {
        'messageId': messageId,
        'action': action, // 'send', 'update', 'delete'
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'retryCount': 0,
      };

      await _syncQueueBox!.put(messageId, syncItem);
      debugPrint('📋 Added to sync queue: $messageId ($action)');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding to sync queue: $e');
      return false;
    }
  }

  /// Get sync queue items
  static Future<List<Map<String, dynamic>>> getSyncQueue() async {
    try {
      await _ensureInitialized();

      final syncItems =
          _syncQueueBox!.values
              .map((item) => Map<String, dynamic>.from(item))
              .toList();

      // Sort by timestamp (oldest first)
      syncItems.sort(
        (a, b) => (a['timestamp'] as int).compareTo(b['timestamp'] as int),
      );

      return syncItems;
    } catch (e) {  
      debugPrint('❌ Error getting sync queue: $e');
      return [];
    }
  }

  /// Remove item from sync queue
  static Future<bool> removeFromSyncQueue(String messageId) async {
    try {
      await _ensureInitialized();
      await _syncQueueBox!.delete(messageId);
      debugPrint('✅ Removed from sync queue: $messageId');
      return true;
    } catch (e) {
      debugPrint('❌ Error removing from sync queue: $e');
      return false;
    }
  }

  /// Update chat metadata (last message, timestamp, etc.)
  static Future<void> _updateChatMetadata(
    String chatId,
    LocalMessage message,
  ) async {
    try {
      final metadata = {
        'chatId': chatId,
        'lastMessageId': message.id,
        'lastMessageText':
            message.text ??
            (message.messageType == MessageType.image.index
                ? '📷 Image'
                : 'Message'),
        'lastMessageTimestamp': message.timestamp,
        'lastMessageSenderId': message.senderId,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      };

      await _chatsBox!.put(chatId, metadata);
    } catch (e) {
      debugPrint('❌ Error updating chat metadata: $e');
    }
  }

  /// Get chat metadata
  static Future<Map<String, dynamic>?> getChatMetadata(String chatId) async {
    try {
      await _ensureInitialized();
      final metadata = _chatsBox!.get(chatId);
      return metadata != null ? Map<String, dynamic>.from(metadata) : null;
    } catch (e) {
      debugPrint('❌ Error getting chat metadata: $e');
      return null;
    }
  }

  /// Get all chats with metadata
  static Future<List<Map<String, dynamic>>> getAllChatsMetadata() async {
    try {
      await _ensureInitialized();

      final chatsMetadata =
          _chatsBox!.values
              .map((metadata) => Map<String, dynamic>.from(metadata))
              .toList();

      // Sort by last message timestamp (newest first)
      chatsMetadata.sort(
        (a, b) => (b['lastMessageTimestamp'] as int).compareTo(
          a['lastMessageTimestamp'] as int,
        ),
      );

      return chatsMetadata;
    } catch (e) {
      debugPrint('❌ Error getting all chats metadata: $e');
      return [];
    }
  }

  /// Clear all local data
  static Future<bool> clearAllData() async {
    try {
      await _ensureInitialized();

      await _messagesBox!.clear();
      await _chatsBox!.clear();
      await _syncQueueBox!.clear();

      debugPrint('🧹 Cleared all local chat data');
      return true;
    } catch (e) {
      debugPrint('❌ Error clearing local data: $e');
      return false;
    }
  }

  /// Clear messages for a specific chat
  static Future<bool> clearChatMessages(String chatId) async {
    try {
      await _ensureInitialized();

      // Get all messages for this chat
      final messagesToDelete = <String>[];
      for (final entry in _messagesBox!.toMap().entries) {
        final message = entry.value;
        if (message.chatId == chatId) {
          messagesToDelete.add(entry.key);
        }
      }

      // Delete messages
      for (final messageId in messagesToDelete) {
        await _messagesBox!.delete(messageId);
      }

      debugPrint(
        '🧹 Cleared ${messagesToDelete.length} local messages for chat: $chatId',
      );
      return true;
    } catch (e) {
      debugPrint('❌ Error clearing local messages for chat $chatId: $e');
      return false;
    }
  }

  /// Get database statistics
  static Future<Map<String, int>> getDatabaseStats() async {
    try {
      await _ensureInitialized();

      return {
        'totalMessages': _messagesBox!.length,
        'totalChats': _chatsBox!.length,
        'syncQueueItems': _syncQueueBox!.length,
        'unsentMessages': (await getUnsentMessages()).length,
      };
    } catch (e) {
      debugPrint('❌ Error getting database stats: $e');
      return {};
    }
  }

  /// Close all boxes (call when app is closing)
  static Future<void> close() async {
    try {
      await _messagesBox?.close();
      await _chatsBox?.close();
      await _syncQueueBox?.close();
      _initialized = false;
      debugPrint('📦 HiveChatService closed');
    } catch (e) {
      debugPrint('❌ Error closing HiveChatService: $e');
    }
  }
}
