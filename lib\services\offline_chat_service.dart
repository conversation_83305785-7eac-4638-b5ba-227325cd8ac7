import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/models/local_message.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/hive_chat_service.dart';
import 'package:mr_garments_mobile/services/connectivity_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/services/whatsapp_local_storage_service.dart';
import 'package:mr_garments_mobile/services/message_sync_service.dart';

/// Enhanced chat service with offline support
/// This service wraps the existing ChatService and adds local storage capabilities
class OfflineChatService {
  static OfflineChatService? _instance;
  static OfflineChatService get instance =>
      _instance ??= OfflineChatService._();
  OfflineChatService._();

  final ConnectivityService _connectivityService = ConnectivityService.instance;
  final MessageSyncService _syncService = MessageSyncService.instance;
  StreamSubscription<bool>? _connectivitySubscription;
  bool _initialized = false;
  bool _syncInProgress = false;

  /// Initialize the offline chat service
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Initialize dependencies
      await HiveChatService.initialize();
      await _connectivityService.initialize();
      await WhatsAppLocalStorageService.initialize();
      await _syncService.initialize();

      // Listen to connectivity changes for auto-sync
      _connectivitySubscription = _connectivityService.connectionStatusStream
          .listen((isConnected) {
            if (isConnected && !_syncInProgress) {
              _syncUnsentMessages();
            }
          });

      _initialized = true;
      debugPrint('✅ OfflineChatService initialized');
    } catch (e) {
      debugPrint('❌ Error initializing OfflineChatService: $e');
      rethrow;
    }
  }

  /// Send text message with offline support
  Future<bool> sendTextMessage({
    required String chatId,
    required String text,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Create message
      final messageId = _generateMessageId();
      final message = Message(
        id: messageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.text,
        text: text,
        timestamp: DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sending,
        isLocalOnly: !_connectivityService.isConnected,
      );

      // Store locally first
      final localMessage = LocalMessage.fromMessage(message, chatId);
      await HiveChatService.storeMessage(localMessage);

      // If online, try to send to Firebase
      if (_connectivityService.isConnected) {
        try {
          await ChatService.sendTextMessage(
            chatId: chatId,
            text: text,
            replyToMessageId: replyToMessageId,
            replyToText: replyToText,
            replyToSenderName: replyToSenderName,
          );

          // Mark as sent in local storage
          await HiveChatService.markMessageAsSent(messageId);
        } catch (e) {
          debugPrint('❌ Failed to send message to Firebase: $e');
          // Add to sync queue for later
          await HiveChatService.addToSyncQueue(messageId, 'send');
        }
      } else {
        // Add to sync queue for when we're back online
        await HiveChatService.addToSyncQueue(messageId, 'send');
        debugPrint('📵 Message queued for sync: $messageId');
      }

      return true;
    } catch (e) {
      debugPrint('❌ Error sending text message: $e');
      return false;
    }
  }

  /// Send image message with offline support
  Future<bool> sendImageMessage({
    required String chatId,
    required String imagePath,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Store image locally first
      final imageFile = File(imagePath);
      final messageId = _generateMessageId();

      final storageResult =
          await WhatsAppLocalStorageService.storeImageAsSender(
            sourceFile: imageFile,
            messageId: messageId,
            chatId: chatId,
          );

      if (!storageResult.success) {
        throw Exception('Failed to store image locally');
      }

      // Create message with local path
      final message = Message(
        id: messageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.image,
        timestamp: DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sending,
        localImagePath: storageResult.localImagePath,
        localThumbnailPath: storageResult.thumbnailPath,
        isLocalOnly: !_connectivityService.isConnected,
      );

      // Store locally
      final localMessage = LocalMessage.fromMessage(message, chatId);
      await HiveChatService.storeMessage(localMessage);

      // If online, try to send to Firebase
      if (_connectivityService.isConnected) {
        try {
          await ChatService.sendImageMessage(
            chatId: chatId,
            imageFile: imageFile,
            replyToMessageId: replyToMessageId,
            replyToText: replyToText,
            replyToSenderName: replyToSenderName,
          );

          // Mark as sent in local storage
          await HiveChatService.markMessageAsSent(messageId);
        } catch (e) {
          debugPrint('❌ Failed to send image to Firebase: $e');
          // Add to sync queue for later
          await HiveChatService.addToSyncQueue(messageId, 'send');
        }
      } else {
        // Add to sync queue for when we're back online
        await HiveChatService.addToSyncQueue(messageId, 'send');
        debugPrint('📵 Image message queued for sync: $messageId');
      }

      return true;
    } catch (e) {
      debugPrint('❌ Error sending image message: $e');
      return false;
    }
  }

  /// Get messages for a chat (from local storage first, then Firebase)
  Future<List<Message>> getMessagesForChat(
    String chatId, {
    int limit = 50,
    int? beforeTimestamp,
  }) async {
    try {
      // Get messages from local storage first
      final localMessages = await HiveChatService.getMessagesForChat(
        chatId,
        limit: limit,
        beforeTimestamp: beforeTimestamp,
      );

      // Convert to Message objects
      final messages =
          localMessages.map((localMsg) => localMsg.toMessage()).toList();

      // If online and we have fewer messages than requested, try to fetch more from Firebase
      if (_connectivityService.isConnected && messages.length < limit) {
        try {
          // This would require modifying the original ChatService to return messages
          // For now, we'll rely on the existing Firebase listeners to populate local storage
        } catch (e) {
          debugPrint('❌ Error fetching additional messages from Firebase: $e');
        }
      }

      return messages;
    } catch (e) {
      debugPrint('❌ Error getting messages for chat: $e');
      return [];
    }
  }

  /// Sync unsent messages when connectivity is restored
  Future<void> _syncUnsentMessages() async {
    if (_syncInProgress) return;

    _syncInProgress = true;
    debugPrint('🔄 Starting message sync...');

    try {
      final unsentMessages = await HiveChatService.getUnsentMessages();
      debugPrint('📤 Found ${unsentMessages.length} unsent messages to sync');

      for (final localMessage in unsentMessages) {
        try {
          final message = localMessage.toMessage();

          // Send based on message type
          if (message.type == MessageType.text) {
            await ChatService.sendTextMessage(
              chatId: localMessage.chatId,
              text: message.text!,
              replyToMessageId: message.replyToMessageId,
              replyToText: message.replyToText,
              replyToSenderName: message.replyToSenderName,
            );
          } else if (message.type == MessageType.image &&
              localMessage.localPath != null) {
            await ChatService.sendImageMessage(
              chatId: localMessage.chatId,
              imageFile: File(localMessage.localPath!),
              replyToMessageId: message.replyToMessageId,
              replyToText: message.replyToText,
              replyToSenderName: message.replyToSenderName,
            );
          }

          // Mark as sent and remove from sync queue
          await HiveChatService.markMessageAsSent(localMessage.id);
          await HiveChatService.removeFromSyncQueue(localMessage.id);

          debugPrint('✅ Synced message: ${localMessage.id}');
        } catch (e) {
          debugPrint('❌ Failed to sync message ${localMessage.id}: $e');
          // Keep in sync queue for next attempt
        }
      }

      debugPrint('🔄 Message sync completed');
    } catch (e) {
      debugPrint('❌ Error during message sync: $e');
    } finally {
      _syncInProgress = false;
    }
  }

  /// Store received message locally
  Future<void> storeReceivedMessage(Message message, String chatId) async {
    try {
      final localMessage = LocalMessage.fromMessage(message, chatId);
      await HiveChatService.storeMessage(localMessage);

      // If it's an image message, download and store locally
      if (message.type == MessageType.image && message.mediaUrl != null) {
        try {
          final currentUserId = await SessionService.getUserId();
          if (currentUserId != null) {
            final receiverResult =
                await WhatsAppLocalStorageService.storeImageAsReceiver(
                  downloadUrl: message.mediaUrl!,
                  messageId: message.id,
                  chatId: chatId,
                  senderId: message.senderId,
                );

            // Persist local paths back into Hive so offline UI can load all images in group
            if (receiverResult.success) {
              await HiveChatService.updateLocalPaths(
                message.id,
                localPath: receiverResult.localImagePath,
                localThumbnailPath: receiverResult.thumbnailPath,
              );
            }
          }
        } catch (e) {
          debugPrint('❌ Failed to store received image locally: $e');
        }
      }
    } catch (e) {
      debugPrint('❌ Error storing received message: $e');
    }
  }

  /// Store multiple received messages as a group (for image grouping)
  Future<void> storeReceivedMessageGroup(
    List<Message> messages,
    String chatId,
  ) async {
    try {
      // Store all messages in the group
      final localMessages =
          messages
              .map((message) => LocalMessage.fromMessage(message, chatId))
              .toList();
      await HiveChatService.storeMessages(localMessages);

      // Download and store all images in the group
      for (final message in messages) {
        if (message.type == MessageType.image && message.mediaUrl != null) {
          try {
            final currentUserId = await SessionService.getUserId();
            if (currentUserId != null) {
              final receiverResult =
                  await WhatsAppLocalStorageService.storeImageAsReceiver(
                    downloadUrl: message.mediaUrl!,
                    messageId: message.id,
                    chatId: chatId,
                    senderId: message.senderId,
                  );

              if (receiverResult.success) {
                await HiveChatService.updateLocalPaths(
                  message.id,
                  localPath: receiverResult.localImagePath,
                  localThumbnailPath: receiverResult.thumbnailPath,
                );
              }
            }
          } catch (e) {
            debugPrint('❌ Failed to store received image locally: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('❌ Error storing received message group: $e');
    }
  }

  /// Generate unique message ID
  String _generateMessageId() {
    return DateTime.now().millisecondsSinceEpoch.toString() +
        (1000 + Random().nextInt(9000)).toString();
  }

  /// Get connectivity status
  bool get isOnline => _connectivityService.isConnected;

  /// Get sync status
  bool get isSyncInProgress => _syncService.isSyncInProgress;

  /// Manual sync trigger
  Future<void> syncNow() async {
    if (_connectivityService.isConnected) {
      await _syncService.syncNow();
    }
  }

  /// Get database statistics
  Future<Map<String, int>> getDatabaseStats() async {
    return await HiveChatService.getDatabaseStats();
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityService.dispose();
    _syncService.dispose();
    debugPrint('📦 OfflineChatService disposed');
  }
}
