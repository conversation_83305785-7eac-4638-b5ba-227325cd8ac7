import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/providers/offline_chat_provider.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/grouped_image_message.dart';

class OfflineChatScreen extends ConsumerStatefulWidget {
  final String chatId;
  final String chatName;
  final String? otherUserProfileUrl;

  const OfflineChatScreen({
    super.key,
    required this.chatId,
    required this.chatName,
    this.otherUserProfileUrl,
  });

  @override
  ConsumerState<OfflineChatScreen> createState() => _OfflineChatScreenState();
}

class _OfflineChatScreenState extends ConsumerState<OfflineChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Load more messages when scrolled to top
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      final notifier = ref.read(offlineChatProvider(widget.chatId).notifier);
      notifier.loadMoreMessages();
    }
  }

  @override
  Widget build(BuildContext context) {
    final chatState = ref.watch(offlineChatProvider(widget.chatId));
    final chatNotifier = ref.read(offlineChatProvider(widget.chatId).notifier);

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: _buildAppBar(chatState, chatNotifier),
      body: Column(
        children: [
          // Connectivity status bar
          _buildConnectivityStatusBar(chatState),

          // Messages list
          Expanded(child: _buildMessagesList(chatState, chatNotifier)),

          // Reply bar (if replying to a message)
          if (chatState.replyToMessage != null)
            _buildReplyBar(chatState.replyToMessage!, chatNotifier),

          // Message input
          _buildMessageInput(chatNotifier),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
    OfflineChatState chatState,
    OfflineChatNotifier chatNotifier,
  ) {
    return AppBar(
      backgroundColor: const Color(0xFF005368),
      foregroundColor: Colors.white,
      elevation: 1,
      title: Row(
        children: [
          // Profile picture
          CircleAvatar(
            radius: 18,
            backgroundColor: Colors.white.withOpacity(0.2),
            backgroundImage:
                widget.otherUserProfileUrl != null
                    ? NetworkImage(widget.otherUserProfileUrl!)
                    : null,
            child:
                widget.otherUserProfileUrl == null
                    ? Text(
                      widget.chatName.isNotEmpty
                          ? widget.chatName[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                    : null,
          ),
          const SizedBox(width: 12),

          // Chat name and status
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.chatName,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                Text(
                  chatNotifier.getConnectivityStatusText(),
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),

          // Connectivity icon
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              chatNotifier.getConnectivityIcon(),
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
      actions: [
        // Sync button (only show when offline and has unsent messages)
        if (!chatState.isOnline && chatNotifier.unsentMessageCount > 0)
          IconButton(
            icon: const Icon(LucideIcons.refreshCw),
            onPressed: () => chatNotifier.syncNow(),
            tooltip: 'Sync messages',
          ),

        // More options
        PopupMenuButton<String>(
          icon: const Icon(LucideIcons.moreVertical),
          onSelected: (value) {
            switch (value) {
              case 'sync':
                chatNotifier.syncNow();
                break;
              case 'stats':
                _showDatabaseStats(chatState);
                break;
            }
          },
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'sync',
                  child: Row(
                    children: [
                      Icon(LucideIcons.refreshCw, size: 16),
                      SizedBox(width: 8),
                      Text('Sync Now'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'stats',
                  child: Row(
                    children: [
                      Icon(LucideIcons.barChart3, size: 16),
                      SizedBox(width: 8),
                      Text('Database Stats'),
                    ],
                  ),
                ),
              ],
        ),
      ],
    );
  }

  Widget _buildConnectivityStatusBar(OfflineChatState chatState) {
    if (chatState.isOnline && !chatState.isSyncInProgress) {
      return const SizedBox.shrink();
    }

    Color backgroundColor;
    String message;
    IconData icon;

    if (chatState.isSyncInProgress) {
      backgroundColor = Colors.blue;
      message = 'Syncing messages...';
      icon = LucideIcons.refreshCw;
    } else {
      backgroundColor = Colors.orange;
      message = 'You\'re offline. Messages will sync when connected.';
      icon = LucideIcons.wifiOff;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: backgroundColor,
      child: Row(
        children: [
          Icon(icon, color: Colors.white, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: GoogleFonts.inter(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (chatState.isSyncInProgress)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMessagesList(
    OfflineChatState chatState,
    OfflineChatNotifier chatNotifier,
  ) {
    if (chatState.isLoading && chatState.messages.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF005368)),
      );
    }

    // Filter out catalog messages when offline
    final filteredMessages =
        chatState.messages
            .where((message) => message.type != MessageType.catalog)
            .toList();

    if (filteredMessages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(LucideIcons.messageCircle, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No messages yet',
              style: GoogleFonts.inter(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start a conversation!',
              style: GoogleFonts.inter(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      reverse: true,
      padding: const EdgeInsets.all(16),
      itemCount: filteredMessages.length + (chatState.isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        if (chatState.isLoading && index == filteredMessages.length) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: CircularProgressIndicator(color: Color(0xFF005368)),
            ),
          );
        }

        final message = filteredMessages[index];
        return _buildMessageBubble(message, chatNotifier);
      },
    );
  }

  Widget _buildMessageBubble(
    Message message,
    OfflineChatNotifier chatNotifier,
  ) {
    final isLocalOnly = chatNotifier.isMessageLocalOnly(message);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        children: [
          // Local only indicator
          if (isLocalOnly)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              margin: const EdgeInsets.only(bottom: 4),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(LucideIcons.clock, size: 12, color: Colors.orange[700]),
                  const SizedBox(width: 4),
                  Text(
                    'Pending sync',
                    style: GoogleFonts.inter(
                      fontSize: 10,
                      color: Colors.orange[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

          // Message bubble with proper image grouping support
          _buildOfflineMessageBubble(message, chatNotifier),
        ],
      ),
    );
  }

  Widget _buildOfflineMessageBubble(
    Message message,
    OfflineChatNotifier chatNotifier,
  ) {
    if (message.type == MessageType.image) {
      // For image messages, check if this is part of a group
      return _buildImageMessage(message, chatNotifier);
    }

    // For text messages, use the simple bubble
    return MessageBubble(
      message: message,
      onReply: () => chatNotifier.setReplyToMessage(message),
      onLongPress: () {
        // Handle long press actions
      },
    );
  }

  Widget _buildImageMessage(Message message, OfflineChatNotifier chatNotifier) {
    // Get all messages to check for image grouping
    final allMessages = ref.read(offlineChatProvider(widget.chatId)).messages;
    final messageIndex = allMessages.indexOf(message);

    // Check if this is the first image in a group
    bool isFirstInGroup = true;
    if (messageIndex > 0) {
      final prevMessage = allMessages[messageIndex - 1];
      if (prevMessage.senderId == message.senderId &&
          prevMessage.type == MessageType.image &&
          message.timestamp.difference(prevMessage.timestamp).inMinutes.abs() <=
              1) {
        isFirstInGroup = false;
      }
    }

    if (!isFirstInGroup) {
      // This is a subsequent image in a group, don't show it separately
      return const SizedBox.shrink();
    }

    // This is the first image in a group, collect all images in the group
    final imageGroup = <Message>[];
    final imageUrls = <String>[];

    for (int i = messageIndex; i < allMessages.length; i++) {
      final currentMessage = allMessages[i];

      // Stop if we hit a different sender or non-image message
      if (currentMessage.senderId != message.senderId ||
          currentMessage.type != MessageType.image) {
        break;
      }

      // Stop if the time gap is too large (more than 1 minute)
      if (i > messageIndex &&
          currentMessage.timestamp
                  .difference(message.timestamp)
                  .inMinutes
                  .abs() >
              1) {
        break;
      }

      imageGroup.add(currentMessage);

      // Get the best image path (local first, then remote)
      final imagePath = currentMessage.bestImagePath ?? currentMessage.mediaUrl;
      if (imagePath != null && imagePath.isNotEmpty) {
        imageUrls.add(imagePath);
      }
    }

    if (imageUrls.isEmpty) {
      return const SizedBox.shrink();
    }

    // Determine if this is from current user
    // We'll use a FutureBuilder to get the current user ID
    return FutureBuilder<bool>(
      future: chatNotifier.isMessageFromCurrentUser(message),
      builder: (context, snapshot) {
        final isMe = snapshot.data ?? false;

        return GroupedImageMessage(
          imageUrls: imageUrls,
          imageMessages: imageGroup,
          senderName: message.senderName,
          timestamp: _formatMessageTime(message.timestamp),
          isMe: isMe,
          chatId: widget.chatId,
          onLongPress: () {
            // Handle long press actions
          },
        );
      },
    );
  }

  String _formatMessageTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  Widget _buildReplyBar(
    Message replyMessage,
    OfflineChatNotifier chatNotifier,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF005368),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Replying to ${replyMessage.senderName}',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF005368),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  replyMessage.text ?? '📷 Image',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(LucideIcons.x, size: 20),
            onPressed: () => chatNotifier.clearReplyMessage(),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput(OfflineChatNotifier chatNotifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          // Attachment button
          IconButton(
            icon: const Icon(LucideIcons.paperclip, color: Color(0xFF005368)),
            onPressed: () => _showAttachmentOptions(chatNotifier),
          ),

          // Text input
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(24),
              ),
              child: TextField(
                controller: _messageController,
                decoration: InputDecoration(
                  hintText: 'Type a message...',
                  hintStyle: GoogleFonts.inter(color: Colors.grey[500]),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                maxLines: null,
                textCapitalization: TextCapitalization.sentences,
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Send button
          GestureDetector(
            onTap: () => _sendMessage(chatNotifier),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                color: Color(0xFF005368),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                LucideIcons.send,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _sendMessage(OfflineChatNotifier chatNotifier) async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    _messageController.clear();

    final success = await chatNotifier.sendTextMessage(text);
    if (!success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to send message'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showAttachmentOptions(OfflineChatNotifier chatNotifier) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(
                    LucideIcons.image,
                    color: Color(0xFF005368),
                  ),
                  title: const Text('Photo'),
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(chatNotifier);
                  },
                ),
                ListTile(
                  leading: const Icon(
                    LucideIcons.camera,
                    color: Color(0xFF005368),
                  ),
                  title: const Text('Camera'),
                  onTap: () {
                    Navigator.pop(context);
                    _takePhoto(chatNotifier);
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _pickImage(OfflineChatNotifier chatNotifier) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
      );
      if (image != null) {
        final success = await chatNotifier.sendImageMessage(image.path);
        if (!success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to send image'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _takePhoto(OfflineChatNotifier chatNotifier) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
      );
      if (image != null) {
        final success = await chatNotifier.sendImageMessage(image.path);
        if (!success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to send image'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error taking photo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDatabaseStats(OfflineChatState chatState) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Database Statistics'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Total Messages: ${chatState.databaseStats['totalMessages'] ?? 0}',
                ),
                Text(
                  'Total Chats: ${chatState.databaseStats['totalChats'] ?? 0}',
                ),
                Text(
                  'Unsent Messages: ${chatState.databaseStats['unsentMessages'] ?? 0}',
                ),
                Text(
                  'Sync Queue: ${chatState.databaseStats['syncQueueItems'] ?? 0}',
                ),
                const SizedBox(height: 8),
                Text('Status: ${chatState.isOnline ? "Online" : "Offline"}'),
                if (chatState.isSyncInProgress) const Text('Sync: In Progress'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }
}

// Simple message bubble widget (you can replace with your existing one)
class MessageBubble extends StatelessWidget {
  final Message message;
  final VoidCallback onReply;
  final VoidCallback onLongPress;

  const MessageBubble({
    super.key,
    required this.message,
    required this.onReply,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    // This is a simplified message bubble
    // You can replace this with your existing message bubble implementation
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment:
            message.senderId == 'current_user'
                ? MainAxisAlignment.end
                : MainAxisAlignment.start,
        children: [
          Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.7,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color:
                  message.senderId == 'current_user'
                      ? const Color(0xFF005368)
                      : Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              message.text ?? '📷 Image',
              style: GoogleFonts.inter(
                color:
                    message.senderId == 'current_user'
                        ? Colors.white
                        : Colors.black87,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
