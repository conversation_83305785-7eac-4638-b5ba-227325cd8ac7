import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/utils/message_utils.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

/// Extension class for ChatService to handle chat-related functionality
class ChatServiceUpdate {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final CollectionReference _chatsCollection = _firestore.collection(
    'chats',
  );

  /// Forward message(s) to multiple chats
  static Future<void> forwardMessages({
    required String fromChatId,
    required String messageId,
    required List<String> toChatIds,
  }) async {
    try {
      // Get original message and check for image grouping
      final messagesCollection = _chatsCollection
          .doc(fromChatId)
          .collection('messages');
      final messageDoc = await messagesCollection.doc(messageId).get();
      if (!messageDoc.exists) throw Exception('Message not found');

      final originalMessage = Message.fromDocument(messageDoc);

      // Get current user details
      final userIdInt = await SessionService.getUserId();
      final currentUserId = userIdInt?.toString();
      final currentUserName = await SessionService.getUserName();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Get messages in the group if it's an image message
      final messageGroup = await MessageUtils.getImageGroupMessages(
        chatId: fromChatId,
        targetMessage: originalMessage,
        messagesCollection: messagesCollection,
      );

      // Forward to each chat
      for (final chatId in toChatIds) {
        final chatDoc = await _chatsCollection.doc(chatId).get();
        if (!chatDoc.exists) continue;

        final chatData = chatDoc.data() as Map<String, dynamic>;
        final chatType = chatData['type'] as String?;

        if (chatType == 'group' && currentUserRole == 'admin') {
          await _forwardToGroupChat(
            groupId: chatId,
            messages: messageGroup,
            currentUserId: currentUserId,
            currentUserName: currentUserName,
          );
        } else {
          await _forwardToIndividualChat(
            chatId: chatId,
            messages: messageGroup,
            currentUserId: currentUserId,
            currentUserName: currentUserName,
          );
        }
      }
    } catch (e) {
      throw Exception('Failed to forward messages: $e');
    }
  }

  /// Forward messages to a group chat
  static Future<void> _forwardToGroupChat({
    required String groupId,
    required List<Message> messages,
    required String currentUserId,
    required String currentUserName,
  }) async {
    try {
      // Forward messages to group chat
      await MessageUtils.forwardMessageGroup(
        messages: messages,
        toChatId: groupId,
        currentUserId: currentUserId,
        currentUserName: currentUserName,
        chatsCollection: _chatsCollection,
      );

      // Get member IDs for broadcast (excluding admin)
      final chatDoc = await _chatsCollection.doc(groupId).get();
      if (!chatDoc.exists) return;

      final chatData = chatDoc.data() as Map<String, dynamic>;
      final memberIds =
          List<String>.from(
            chatData['memberIds'] ?? [],
          ).where((id) => id != currentUserId).toList();

      // Forward to individual chats of all members
      for (final memberId in memberIds) {
        try {
          final individualChatId = await _getOrCreateIndividualChat(
            currentUserId: currentUserId,
            memberId: memberId,
          );

          if (individualChatId != null) {
            await MessageUtils.forwardMessageGroup(
              messages: messages,
              toChatId: individualChatId,
              currentUserId: currentUserId,
              currentUserName: currentUserName,
              chatsCollection: _chatsCollection,
            );

            // Update unread count and send notification
            await _chatsCollection.doc(individualChatId).update({
              'unreadCounts.$memberId': FieldValue.increment(messages.length),
            });

            // Send notification for forwarded messages
            await _sendNotificationToMembers(
              memberIds: [memberId],
              chatId: individualChatId,
              senderName: currentUserName,
              messageText: _getNotificationTextForGroup(messages),
              messageType: messages.first.type.name,
            );
          }
        } catch (e) {
          // print('Failed to forward to member $memberId: $e');
          continue;
        }
      }
    } catch (e) {
      throw Exception('Failed to forward to group chat: $e');
    }
  }

  /// Forward messages to an individual chat
  static Future<void> _forwardToIndividualChat({
    required String chatId,
    required List<Message> messages,
    required String currentUserId,
    required String currentUserName,
  }) async {
    await MessageUtils.forwardMessageGroup(
      messages: messages,
      toChatId: chatId,
      currentUserId: currentUserId,
      currentUserName: currentUserName,
      chatsCollection: _chatsCollection,
    );

    // Get chat data to update unread counts and send notifications
    final chatDoc = await _chatsCollection.doc(chatId).get();
    if (chatDoc.exists) {
      final chatData = chatDoc.data() as Map<String, dynamic>;
      final memberIds = List<String>.from(chatData['memberIds'] ?? []);
      final otherMemberIds =
          memberIds.where((id) => id != currentUserId).toList();

      if (otherMemberIds.isNotEmpty) {
        // Update unread counts
        for (final memberId in otherMemberIds) {
          await _chatsCollection.doc(chatId).update({
            'unreadCounts.$memberId': FieldValue.increment(messages.length),
          });
        }

        // Send notification for forwarded messages
        await _sendNotificationToMembers(
          memberIds: otherMemberIds,
          chatId: chatId,
          senderName: currentUserName,
          messageText: _getNotificationTextForGroup(messages),
          messageType: messages.first.type.name,
        );
      }
    }
  }

  /// Get or create individual chat for a member
  static Future<String?> _getOrCreateIndividualChat({
    required String currentUserId,
    required String memberId,
  }) async {
    try {
      // Generate consistent chat ID
      final sortedIds = [currentUserId, memberId]..sort();
      final chatId = '${sortedIds[0]}_${sortedIds[1]}';

      // Return existing chat ID if it exists
      final existingChat = await _chatsCollection.doc(chatId).get();
      if (existingChat.exists) {
        return chatId;
      }

      // Otherwise create new chat
      await _chatsCollection.doc(chatId).set({
        'id': chatId,
        'type': 'individual',
        'memberIds': [currentUserId, memberId],
        'isActive': true,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      return chatId;
    } catch (e) {
      // print('Failed to get/create individual chat: $e');
      return null;
    }
  }
}
