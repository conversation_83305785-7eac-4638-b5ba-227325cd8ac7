import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/services/offline_chat_service.dart';
import 'package:mr_garments_mobile/services/connectivity_service.dart';
import 'package:mr_garments_mobile/services/hive_chat_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

// ==================== OFFLINE CHAT STATE ====================

class OfflineChatState {
  final List<Message> messages;
  final bool isLoading;
  final bool isOnline;
  final bool isSyncInProgress;
  final String? error;
  final Message? replyToMessage;
  final Map<String, int> databaseStats;

  const OfflineChatState({
    this.messages = const [],
    this.isLoading = false,
    this.isOnline = false,
    this.isSyncInProgress = false,
    this.error,
    this.replyToMessage,
    this.databaseStats = const {},
  });

  OfflineChatState copyWith({
    List<Message>? messages,
    bool? isLoading,
    bool? isOnline,
    bool? isSyncInProgress,
    String? error,
    Message? replyToMessage,
    Map<String, int>? databaseStats,
  }) {
    return OfflineChatState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      isOnline: isOnline ?? this.isOnline,
      isSyncInProgress: isSyncInProgress ?? this.isSyncInProgress,
      error: error ?? this.error,
      replyToMessage: replyToMessage ?? this.replyToMessage,
      databaseStats: databaseStats ?? this.databaseStats,
    );
  }

  @override
  String toString() {
    return 'OfflineChatState(messages: ${messages.length}, isOnline: $isOnline, isSyncInProgress: $isSyncInProgress)';
  }
}

// ==================== OFFLINE CHAT NOTIFIER ====================

class OfflineChatNotifier extends StateNotifier<OfflineChatState> {
  final String chatId;
  final OfflineChatService _offlineChatService = OfflineChatService.instance;
  final ConnectivityService _connectivityService = ConnectivityService.instance;

  StreamSubscription<bool>? _connectivitySubscription;
  Timer? _refreshTimer;
  bool _initialized = false;

  OfflineChatNotifier(this.chatId) : super(const OfflineChatState()) {
    _initialize();
  }

  /// Initialize the offline chat notifier
  Future<void> _initialize() async {
    if (_initialized) return;

    try {
      state = state.copyWith(isLoading: true);

      // Initialize services
      await _offlineChatService.initialize();

      // Listen to connectivity changes
      _connectivitySubscription = _connectivityService.connectionStatusStream
          .listen((isOnline) {
            state = state.copyWith(
              isOnline: isOnline,
              isSyncInProgress:
                  isOnline ? _offlineChatService.isSyncInProgress : false,
            );

            if (isOnline) {
              _refreshMessages();
            }
          });

      // Set initial connectivity status
      state = state.copyWith(isOnline: _connectivityService.isConnected);

      // Load initial messages
      await _loadMessages();

      // Set up periodic refresh for sync status
      _refreshTimer = Timer.periodic(const Duration(seconds: 5), (_) {
        _updateSyncStatus();
      });

      _initialized = true;
      debugPrint('✅ OfflineChatNotifier initialized for chat: $chatId');
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize offline chat: $e',
      );
      debugPrint('❌ Error initializing OfflineChatNotifier: $e');
    }
  }

  /// Load messages from local storage
  Future<void> _loadMessages() async {
    try {
      final messages = await _offlineChatService.getMessagesForChat(chatId);

      state = state.copyWith(messages: messages, isLoading: false, error: null);

      debugPrint('📱 Loaded ${messages.length} messages for chat $chatId');
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load messages: $e',
      );
      debugPrint('❌ Error loading messages: $e');
    }
  }

  /// Refresh messages (useful for pull-to-refresh)
  Future<void> _refreshMessages() async {
    await _loadMessages();
    await _updateDatabaseStats();
  }

  /// Update sync status
  void _updateSyncStatus() {
    state = state.copyWith(
      isSyncInProgress: _offlineChatService.isSyncInProgress,
    );
  }

  /// Update database statistics
  Future<void> _updateDatabaseStats() async {
    try {
      final stats = await _offlineChatService.getDatabaseStats();
      state = state.copyWith(databaseStats: stats);
    } catch (e) {
      debugPrint('❌ Error updating database stats: $e');
    }
  }

  /// Send text message
  Future<bool> sendTextMessage(String text) async {
    try {
      final success = await _offlineChatService.sendTextMessage(
        chatId: chatId,
        text: text,
        replyToMessageId: state.replyToMessage?.id,
        replyToText: state.replyToMessage?.text,
        replyToSenderName: state.replyToMessage?.senderName,
      );

      if (success) {
        // Clear reply message
        state = state.copyWith(replyToMessage: null);

        // Refresh messages to show the new message
        await _refreshMessages();
      }

      return success;
    } catch (e) {
      state = state.copyWith(error: 'Failed to send message: $e');
      debugPrint('❌ Error sending text message: $e');
      return false;
    }
  }

  /// Send image message
  Future<bool> sendImageMessage(String imagePath) async {
    try {
      final success = await _offlineChatService.sendImageMessage(
        chatId: chatId,
        imagePath: imagePath,
        replyToMessageId: state.replyToMessage?.id,
        replyToText: state.replyToMessage?.text,
        replyToSenderName: state.replyToMessage?.senderName,
      );

      if (success) {
        // Clear reply message
        state = state.copyWith(replyToMessage: null);

        // Refresh messages to show the new message
        await _refreshMessages();
      }

      return success;
    } catch (e) {
      state = state.copyWith(error: 'Failed to send image: $e');
      debugPrint('❌ Error sending image message: $e');
      return false;
    }
  }

  /// Set reply to message
  void setReplyToMessage(Message? message) {
    state = state.copyWith(replyToMessage: message);
  }

  /// Clear reply message
  void clearReplyMessage() {
    state = state.copyWith(replyToMessage: null);
  }

  /// Manual sync trigger
  Future<void> syncNow() async {
    if (state.isOnline) {
      await _offlineChatService.syncNow();
      await _refreshMessages();
    }
  }

  /// Load more messages (pagination)
  Future<void> loadMoreMessages() async {
    if (state.isLoading) return;

    try {
      state = state.copyWith(isLoading: true);

      final currentMessages = state.messages;
      final oldestTimestamp =
          currentMessages.isNotEmpty
              ? currentMessages.last.timestamp.millisecondsSinceEpoch
              : null;

      final moreMessages = await _offlineChatService.getMessagesForChat(
        chatId,
        limit: 20,
        beforeTimestamp: oldestTimestamp,
      );

      if (moreMessages.isNotEmpty) {
        final allMessages = [...currentMessages, ...moreMessages];
        state = state.copyWith(messages: allMessages, isLoading: false);
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load more messages: $e',
      );
      debugPrint('❌ Error loading more messages: $e');
    }
  }

  /// Get connectivity status text for UI
  String getConnectivityStatusText() {
    if (state.isSyncInProgress) {
      return 'Syncing...';
    } else if (state.isOnline) {
      return 'Online';
    } else {
      return 'Offline';
    }
  }

  /// Get connectivity icon for UI
  String getConnectivityIcon() {
    if (state.isSyncInProgress) {
      return '🔄';
    } else if (state.isOnline) {
      return '🟢';
    } else {
      return '🔴';
    }
  }

  /// Check if message is from local storage only
  bool isMessageLocalOnly(Message message) {
    return message.isLocalOnly;
  }

  /// Check if message is sent by current user
  Future<bool> isMessageFromCurrentUser(Message message) async {
    final currentUserId = await SessionService.getUserId();
    return message.senderId == currentUserId.toString();
  }

  /// Get unsent message count
  int get unsentMessageCount {
    return state.databaseStats['unsentMessages'] ?? 0;
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _refreshTimer?.cancel();
    _offlineChatService.dispose();
    super.dispose();
  }
}

// ==================== PROVIDERS ====================

/// Provider for offline chat functionality
final offlineChatProvider =
    StateNotifierProvider.family<OfflineChatNotifier, OfflineChatState, String>(
      (ref, chatId) => OfflineChatNotifier(chatId),
    );

/// Provider for connectivity status
final connectivityStatusProvider = StreamProvider<bool>((ref) {
  return ConnectivityService.instance.connectionStatusStream;
});

/// Provider for database statistics
final chatDatabaseStatsProvider = FutureProvider<Map<String, int>>((ref) async {
  return await HiveChatService.getDatabaseStats();
});
