import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BrandService {
  static const String baseUrl = 'https://mrgindia.com/api';

  // Helper method to get auth token
  static Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }



  static Future<List<Map<String, dynamic>>> fetchBrands() async {
    final token = await _getToken();
    final response = await http.get(Uri.parse('$baseUrl/brands'),
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.cast<Map<String, dynamic>>();
    } else {
      throw Exception('Failed to load brands');
    }
  }

  // Get brands for specific category
  static Future<List<Map<String, dynamic>>> fetchBrandsByCategory(
    int categoryId,
  ) async {
    final token = await _getToken();
    final response = await http.get(
      Uri.parse('$baseUrl/categories/$categoryId/brands'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
    );
    if (response.statusCode == 200) {
      final List data = json.decode(response.body);
      return data.cast<Map<String, dynamic>>();
    } else {
      throw Exception('Failed to load brands for category ID $categoryId');
    }
  }

  // Get catalogs for specific brand
  static Future<List<Map<String, dynamic>>> fetchCatalogsByBrand(
    int brandId,
  ) async {
    final token = await _getToken();
    final response = await http.get(
      Uri.parse('$baseUrl/brands/$brandId/catalogs',),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
    );
    if (response.statusCode == 200) {
      final List data = json.decode(response.body);
      return data.cast<Map<String, dynamic>>();
    } else {
      throw Exception('Failed to load catalogs for brand ID $brandId');
    }
  }

  static Future<Map<String, dynamic>> addBrand({
    required String brandName,
    required String manufacturerName,
    File? imageFile,
  }) async {
    final token = await _getToken();
    final uri = Uri.parse('$baseUrl/brands');
    final request =
        http.MultipartRequest('POST', uri)
          ..fields['brandName'] = brandName
          ..fields['manufacturerName'] = manufacturerName;
    request.headers.addAll({
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    });

    if (imageFile != null) {
      final ext = imageFile.path
          .split('.')
          .last
          .toLowerCase()
          .replaceFirst('.', '');

      final mimeType =
          {
            'jpg': 'jpeg',
            'jpeg': 'jpeg',
            'png': 'png',
            'heic': 'heic',
            'webp': 'webp',
            'bmp': 'bmp',
            'gif': 'gif',
            '': 'jpeg', // Handle missing extensions
          }[ext] ??
          'jpeg';

      request.files.add(
        await http.MultipartFile.fromPath(
          'images[]',
          imageFile.path,
          contentType: MediaType('image', mimeType),
        ),
      );
    }

    final response = await request.send();

    if (response.statusCode == 201) {
      final resBody = await response.stream.bytesToString();
      return json.decode(resBody);
    } else {
      final resBody = await response.stream.bytesToString();
      throw Exception('Failed to add brand: ${response.statusCode} - $resBody');
    }
  }
}
