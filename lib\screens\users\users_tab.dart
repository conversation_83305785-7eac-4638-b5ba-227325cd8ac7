import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/widgets/network_error_widget.dart';
import 'package:mr_garments_mobile/providers/user_provider.dart';
import 'package:mr_garments_mobile/screens/users/add_edit_user.dart';
import 'package:mr_garments_mobile/screens/users/edit_staff.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

class UsersTab extends ConsumerWidget {
  final String searchQuery;
  const UsersTab({super.key, required this.searchQuery});

  Color getStatusColor(String status) {
    return status == 'rejected' ? Colors.red.shade100 : Colors.green.shade100;
  }

  Color getStatusTextColor(String status) {
    return status == 'rejected' ? Colors.red.shade800 : Colors.green.shade800;
  }


  Future<Map<String, dynamic>> _getUserRoleAndId() async {
    final role = await SessionService.getUserRole();
    final id = await SessionService.getUserId();
    return {'role': role, 'id': id};
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usersAsync = ref.watch(usersProvider).users;
    return usersAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) {
        final errorText = e.toString();
        final isNetworkError =
            e is SocketException ||
            errorText.toLowerCase().contains('failed host lookup') ||
            errorText.toLowerCase().contains('socketexception') ||
            errorText.toLowerCase().contains('network');

        if (isNetworkError) {
          return NetworkErrorWidget(
            message: 'Failed to load users',
            onRetry: () {
              // ignore: unused_result
              ref.refresh(usersProvider);
            },
          );
        }

        // fallback for other errors
        return Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text("Error: $errorText"),
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: () => ref.refresh(usersProvider),
                child: const Text('Try Again'),
              ),
            ],
          ),
        );
      },
      data: (users) {
        return FutureBuilder<Map<String, dynamic>>(
          future: _getUserRoleAndId(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            final currentUserRole = snapshot.data?['role'] as String?;
            final currentUserId = snapshot.data?['id'] as int?;

            final activeUsers =
                users.where((u) {
                  final status = u['status']?.toLowerCase();
                  return status == 'approved' ||
                      status == 'active' ||
                      status == 'approve';
                }).toList();

            // Apply role-based filtering
            List<Map<String, dynamic>> roleFilteredUsers;
            if (currentUserRole == 'admin' ||
                currentUserRole == 'salesperson') {
              // Admin and SalesPerson can see all users except themselves
              roleFilteredUsers =
                  activeUsers
                      .where(
                        (u) => u['id']?.toString() != currentUserId?.toString(),
                      )
                      .cast<Map<String, dynamic>>()
                      .toList();
            } else {
              // Non-admin users can only see admin and salesperson users
              roleFilteredUsers =
                  activeUsers
                      .where((u) {
                        final accountType =
                            u['account_type']?.toString().toLowerCase();
                        return accountType == 'admin' ||
                            accountType == 'salesperson';
                      })
                      .cast<Map<String, dynamic>>()
                      .toList();
            }

            // Apply search filter
            final filteredUsers =
                roleFilteredUsers.where((u) {
                  final name = (u['name'] ?? '').toLowerCase();
                  return name.contains(searchQuery.toLowerCase());
                }).toList();

            if (filteredUsers.isEmpty) {
              return const Center(child: Text("No users found"));
            }

            return ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              itemCount: filteredUsers.length,
              itemBuilder: (context, index) {
                final user = filteredUsers[index];

                return Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 3,
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 14,
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const CircleAvatar(
                          radius: 18,

                          backgroundColor: Color(0xFF005368),
                          child: Icon(Icons.person, color: Colors.white),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                user['name'] ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                user['role'] ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.blueGrey,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                user['email'] ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 13,
                                  color: Colors.grey[700],
                                ),
                              ),
                              const SizedBox(height: 8),
                              if (user['status'] != null)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: getStatusColor(
                                      user['status'] ?? 'unknown',
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    (user['status']?.toLowerCase() ==
                                                'active' ||
                                            user['status']?.toLowerCase() ==
                                                'approved')
                                        ? 'Active'
                                        : user['status'],
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      color: getStatusTextColor(user['status']),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // IconButton(
                            //   onPressed:
                            //       () => _startChatWithUser(context, user),
                            //   icon: const Icon(
                            //     Icons.chat,
                            //     color: Color(0xFF005368),
                            //   ),
                            //   tooltip: 'Start Chat',
                            // ),
                            IconButton(
                              onPressed: () async {
                                // Check if user is staff to navigate to appropriate edit screen
                                final accountType =
                                    user['account_type']
                                        ?.toString()
                                        .toLowerCase() ??
                                    '';
                                final jobRole =
                                    user['job_role']
                                        ?.toString()
                                        .toLowerCase() ??
                                    '';
                                final role =
                                    user['role']?.toString().toLowerCase() ??
                                    '';

                                // Check multiple possible fields for staff identification
                                if (accountType == 'staff' ||
                                    jobRole == 'staff' ||
                                    role == 'staff') {
                                  // Navigate to Edit Staff screen
                                  await Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) => EditStaff(staff: user),
                                    ),
                                  );
                                } else {
                                  // Navigate to regular Edit User screen
                                  await Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) => AddEditUser(user: user),
                                    ),
                                  );
                                }
                              },
                              icon: const Icon(
                                Icons.edit,
                                color: Color(0xFFF2A738),
                              ),
                              tooltip: 'Edit User',
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
}
