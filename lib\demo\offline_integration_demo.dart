import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/providers/chat_provider.dart';
import 'package:mr_garments_mobile/providers/offline_chat_provider.dart';
import 'package:mr_garments_mobile/services/connectivity_service.dart';
import 'package:mr_garments_mobile/services/hive_chat_service.dart';

class OfflineIntegrationDemo extends ConsumerStatefulWidget {
  const OfflineIntegrationDemo({super.key});

  @override
  ConsumerState<OfflineIntegrationDemo> createState() => _OfflineIntegrationDemoState();
}

class _OfflineIntegrationDemoState extends ConsumerState<OfflineIntegrationDemo> {
  String _testChatId = 'demo_chat_123';
  String _statusMessage = 'Ready to test offline functionality';

  @override
  Widget build(BuildContext context) {
    final connectivityStatus = ref.watch(connectivityStatusProvider);
    final databaseStats = ref.watch(chatDatabaseStatsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Offline Chat Integration Demo'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Connectivity Status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Connectivity Status',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    connectivityStatus.when(
                      data: (isOnline) => Row(
                        children: [
                          Icon(
                            isOnline ? Icons.wifi : Icons.wifi_off,
                            color: isOnline ? Colors.green : Colors.red,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            isOnline ? 'Online' : 'Offline',
                            style: TextStyle(
                              color: isOnline ? Colors.green : Colors.red,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      loading: () => const Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Checking...'),
                        ],
                      ),
                      error: (error, stack) => Text('Error: $error'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Database Statistics
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Local Database Statistics',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    databaseStats.when(
                      data: (stats) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Total Messages: ${stats['totalMessages'] ?? 0}'),
                          Text('Total Chats: ${stats['totalChats'] ?? 0}'),
                          Text('Unsent Messages: ${stats['unsentMessages'] ?? 0}'),
                          Text('Sync Queue Items: ${stats['syncQueueItems'] ?? 0}'),
                        ],
                      ),
                      loading: () => const Text('Loading statistics...'),
                      error: (error, stack) => Text('Error: $error'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Test Actions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Actions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Test Hybrid Provider
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => _testHybridProvider(),
                        icon: const Icon(Icons.sync),
                        label: const Text('Test Hybrid Provider'),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // View Messages
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => _viewMessages(),
                        icon: const Icon(Icons.message),
                        label: const Text('View Messages (Hybrid)'),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Clear Database
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => _clearDatabase(),
                        icon: const Icon(Icons.delete),
                        label: const Text('Clear Local Database'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red.shade100,
                          foregroundColor: Colors.red.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Status Message
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(_statusMessage),
                  ],
                ),
              ),
            ),

            const Spacer(),

            // Instructions
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'How to Test Offline Functionality:',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '1. Go to any chat screen\n'
                    '2. Send some messages while online\n'
                    '3. Turn off WiFi/mobile data\n'
                    '4. Check that messages still appear\n'
                    '5. Send new messages (they will be queued)\n'
                    '6. Turn internet back on to see sync',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _testHybridProvider() async {
    setState(() {
      _statusMessage = 'Testing hybrid provider...';
    });

    try {
      // Test the hybrid provider by watching it
      final messagesAsync = ref.read(hybridMessagesStreamProvider(_testChatId));
      
      setState(() {
        _statusMessage = 'Hybrid provider test completed successfully!';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Hybrid provider test failed: $e';
      });
    }
  }

  void _viewMessages() async {
    setState(() {
      _statusMessage = 'Loading messages from hybrid provider...';
    });

    try {
      final localMessages = await HiveChatService.getMessagesForChat(_testChatId);
      setState(() {
        _statusMessage = 'Found ${localMessages.length} cached messages for chat $_testChatId';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading messages: $e';
      });
    }
  }

  void _clearDatabase() async {
    setState(() {
      _statusMessage = 'Clearing local database...';
    });

    try {
      await HiveChatService.clearAllData();
      
      // Refresh the database stats
      ref.invalidate(chatDatabaseStatsProvider);
      
      if (mounted) {
        setState(() {
          _statusMessage = 'Local database cleared successfully!';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _statusMessage = 'Error clearing database: $e';
        });
      }
    }
  }
}
