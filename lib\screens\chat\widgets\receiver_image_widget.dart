import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import '../../../models/message.dart';
import '../../../services/receiver_download_service.dart';
import '../../../services/chat_service.dart';
import '../../../utils/enhanced_image_utils.dart';

/// Widget for displaying receiver images with download option
class ReceiverImageWidget extends ConsumerStatefulWidget {
  final Message message;
  final String chatId;
  final double maxWidth;
  final double maxHeight;
  final VoidCallback? onTap;

  const ReceiverImageWidget({
    super.key,
    required this.message,
    required this.chatId,
    required this.maxWidth,
    required this.maxHeight,
    this.onTap,
  });

  @override
  ConsumerState<ReceiverImageWidget> createState() =>
      _ReceiverImageWidgetState();
}

class _ReceiverImageWidgetState extends ConsumerState<ReceiverImageWidget> {
  bool _isDownloading = false;
  double _downloadProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _checkDownloadStatus();
  }

  Future<void> _checkDownloadStatus() async {
    final status = await ReceiverDownloadService.getDownloadStatus(
      widget.message,
    );
    if (mounted) {
      ref.read(downloadStatusProvider(widget.message.id).notifier).state =
          status;
    }
  }

  Future<void> _handleDownload() async {
    if (_isDownloading) return;

    // Check if image is auto-downloaded but not visible yet
    final isAutoDownloaded = widget.message.metadata?['autoDownloaded'] == true;
    final isReadyForDisplay =
        widget.message.metadata?['readyForDisplay'] == true;

    if (isAutoDownloaded && !isReadyForDisplay) {
      // Image is already downloaded, just make it visible
      await _makeImageVisible();
      return;
    }

    // Image not downloaded yet, show download dialog
    final shouldDownload = await ReceiverDownloadService.showDownloadDialog(
      context: context,
      message: widget.message,
      chatId: widget.chatId,
    );

    if (!shouldDownload || !mounted) return;

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    final success = await ReceiverDownloadService.downloadImageForReceiver(
      messageId: widget.message.id,
      chatId: widget.chatId,
      downloadUrl: widget.message.mediaUrl!,
      senderId: widget.message.senderId,
      onProgress: (progress) {
        if (mounted) {
          setState(() {
            _downloadProgress = progress;
          });
        }
      },
    );

    if (mounted) {
      setState(() {
        _isDownloading = false;
      });

      if (success) {
        ref.read(downloadStatusProvider(widget.message.id).notifier).state =
            DownloadStatus.downloaded;

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Image downloaded successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      } else {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to download image'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _makeImageVisible() async {
    try {
      await ChatService.updateMessageMetadata(
        chatId: widget.chatId,
        messageId: widget.message.id,
        metadata: {'readyForDisplay': true},
      );

      ref.read(downloadStatusProvider(widget.message.id).notifier).state =
          DownloadStatus.downloaded;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Image is now visible'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error making image visible'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final downloadStatus = ref.watch(downloadStatusProvider(widget.message.id));
    final isAutoDownloading =
        widget.message.metadata?['autoDownloading'] == true;
    final isReadyForDisplay =
        widget.message.metadata?['readyForDisplay'] == true;

    return GestureDetector(
      onTap: () {
        // If image is downloaded, allow viewing
        if (downloadStatus == DownloadStatus.downloaded || isReadyForDisplay) {
          widget.onTap?.call();
        } else if (!isAutoDownloading) {
          // Only allow manual download if not auto-downloading
          _handleDownload();
        }
      },
      child: Container(
        width: widget.maxWidth,
        height: widget.maxHeight,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.grey[300],
        ),
        child: Stack(
          children: [
            // Background image or placeholder
            _buildImageContent(downloadStatus),

            // Manual download progress overlay (only for user-initiated downloads)
            if (_isDownloading) _buildDownloadProgressOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildImageContent(DownloadStatus status) {
    final isReadyForDisplay =
        widget.message.metadata?['readyForDisplay'] == true;

    if (isReadyForDisplay && widget.message.hasLocalImage) {
      // Show local image if ready for display
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: EnhancedImageUtils.buildChatImage(
          widget.message.localImagePath!,
          width: widget.maxWidth,
          height: widget.maxHeight,
          fit: BoxFit.cover,
          isLocalFile: true,
        ),
      );
    }

    // Show placeholder with thumbnail if available
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: widget.maxWidth,
        height: widget.maxHeight,
        color: Colors.grey[300],
        child: widget.message.thumbnailUrl != null
            ? EnhancedImageUtils.buildChatImage(
                widget.message.thumbnailUrl!,
                width: widget.maxWidth,
                height: widget.maxHeight,
                fit: BoxFit.cover,
              )
            : const Center(
                child: Icon(LucideIcons.image, size: 48, color: Colors.grey),
              ),
      ),
    );
  }

  Widget _buildDownloadProgressOverlay() {
    return Container(
      width: widget.maxWidth,
      height: widget.maxHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.black.withValues(alpha: 0.6),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 48,
              height: 48,
              child: CircularProgressIndicator(
                value: _downloadProgress,
                strokeWidth: 3,
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                backgroundColor: Colors.white.withValues(alpha: 0.3),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${(_downloadProgress * 100).toInt()}%',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}