import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/models/local_message.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/hive_chat_service.dart';
import 'package:mr_garments_mobile/services/connectivity_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

/// Service to handle message synchronization between local storage and Firebase
class MessageSyncService {
  static MessageSyncService? _instance;
  static MessageSyncService get instance => _instance ??= MessageSyncService._();
  MessageSyncService._();

  final ConnectivityService _connectivityService = ConnectivityService.instance;
  StreamSubscription<bool>? _connectivitySubscription;
  Timer? _syncTimer;
  bool _syncInProgress = false;
  bool _initialized = false;

  /// Initialize the sync service
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Listen to connectivity changes
      _connectivitySubscription = _connectivityService.connectionStatusStream.listen(
        (isConnected) {
          if (isConnected && !_syncInProgress) {
            _triggerSync();
          }
        },
      );

      // Set up periodic sync (every 30 seconds when online)
      _syncTimer = Timer.periodic(const Duration(seconds: 30), (_) {
        if (_connectivityService.isConnected && !_syncInProgress) {
          _triggerSync();
        }
      });

      _initialized = true;
      debugPrint('✅ MessageSyncService initialized');
    } catch (e) {
      debugPrint('❌ Error initializing MessageSyncService: $e');
      rethrow;
    }
  }

  /// Trigger sync process
  Future<void> _triggerSync() async {
    if (_syncInProgress || !_connectivityService.isConnected) return;

    _syncInProgress = true;
    debugPrint('🔄 Starting message sync...');

    try {
      // Sync unsent messages
      await _syncUnsentMessages();
      
      // Sync received messages (download new messages from Firebase)
      await _syncReceivedMessages();
      
      // Clean up sync queue
      await _cleanupSyncQueue();

      debugPrint('✅ Message sync completed successfully');
    } catch (e) {
      debugPrint('❌ Error during message sync: $e');
    } finally {
      _syncInProgress = false;
    }
  }

  /// Sync unsent messages to Firebase
  Future<void> _syncUnsentMessages() async {
    try {
      final unsentMessages = await HiveChatService.getUnsentMessages();
      if (unsentMessages.isEmpty) return;

      debugPrint('📤 Syncing ${unsentMessages.length} unsent messages...');

      for (final localMessage in unsentMessages) {
        try {
          await _syncSingleMessage(localMessage);
        } catch (e) {
          debugPrint('❌ Failed to sync message ${localMessage.id}: $e');
          // Continue with other messages
        }
      }
    } catch (e) {
      debugPrint('❌ Error syncing unsent messages: $e');
    }
  }

  /// Sync a single message to Firebase
  Future<void> _syncSingleMessage(LocalMessage localMessage) async {
    try {
      final message = localMessage.toMessage();
      
      // Send based on message type
      switch (message.type) {
        case MessageType.text:
          await ChatService.sendTextMessage(
            chatId: localMessage.chatId,
            text: message.text!,
            replyToMessageId: message.replyToMessageId,
            replyToText: message.replyToText,
            replyToSenderName: message.replyToSenderName,
          );
          break;
          
        case MessageType.image:
          if (localMessage.localPath != null && await File(localMessage.localPath!).exists()) {
            await ChatService.sendImageMessage(
              chatId: localMessage.chatId,
              imageFile: File(localMessage.localPath!),
              replyToMessageId: message.replyToMessageId,
              replyToText: message.replyToText,
              replyToSenderName: message.replyToSenderName,
            );
          } else {
            throw Exception('Local image file not found');
          }
          break;
          
        case MessageType.file:
          // Handle file messages if needed
          debugPrint('⚠️ File message sync not implemented yet');
          break;
          
        default:
          debugPrint('⚠️ Unsupported message type for sync: ${message.type}');
      }

      // Mark as sent and remove from sync queue
      await HiveChatService.markMessageAsSent(localMessage.id);
      await HiveChatService.removeFromSyncQueue(localMessage.id);
      
      debugPrint('✅ Synced message: ${localMessage.id}');
    } catch (e) {
      debugPrint('❌ Failed to sync message ${localMessage.id}: $e');
      
      // Update retry count in sync queue
      await _updateSyncRetryCount(localMessage.id);
      rethrow;
    }
  }

  /// Sync received messages from Firebase
  Future<void> _syncReceivedMessages() async {
    try {
      // This would typically involve listening to Firebase streams
      // and storing new messages locally
      // For now, we'll rely on the existing Firebase listeners
      // to populate the local database
      
      debugPrint('📥 Checking for new messages from Firebase...');
      
      // You can implement additional logic here to fetch messages
      // that might have been missed while offline
      
    } catch (e) {
      debugPrint('❌ Error syncing received messages: $e');
    }
  }

  /// Clean up sync queue (remove old failed items)
  Future<void> _cleanupSyncQueue() async {
    try {
      final syncQueue = await HiveChatService.getSyncQueue();
      final now = DateTime.now().millisecondsSinceEpoch;
      const maxRetryAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

      for (final item in syncQueue) {
        final timestamp = item['timestamp'] as int;
        final retryCount = item['retryCount'] as int;
        final messageId = item['messageId'] as String;

        // Remove items that are too old or have too many retries
        if (now - timestamp > maxRetryAge || retryCount > 5) {
          await HiveChatService.removeFromSyncQueue(messageId);
          debugPrint('🧹 Removed old sync queue item: $messageId');
        }
      }
    } catch (e) {
      debugPrint('❌ Error cleaning up sync queue: $e');
    }
  }

  /// Update retry count for a sync queue item
  Future<void> _updateSyncRetryCount(String messageId) async {
    try {
      final syncQueue = await HiveChatService.getSyncQueue();
      final item = syncQueue.firstWhere(
        (item) => item['messageId'] == messageId,
        orElse: () => <String, dynamic>{},
      );

      if (item.isNotEmpty) {
        final retryCount = (item['retryCount'] as int? ?? 0) + 1;
        await HiveChatService.addToSyncQueue(messageId, item['action'] as String);
        // Note: The addToSyncQueue method would need to be updated to handle retry count
      }
    } catch (e) {
      debugPrint('❌ Error updating sync retry count: $e');
    }
  }

  /// Manual sync trigger (for pull-to-refresh or user action)
  Future<bool> syncNow() async {
    if (!_connectivityService.isConnected) {
      debugPrint('📵 Cannot sync: No internet connection');
      return false;
    }

    if (_syncInProgress) {
      debugPrint('🔄 Sync already in progress');
      return false;
    }

    await _triggerSync();
    return true;
  }

  /// Get sync status
  bool get isSyncInProgress => _syncInProgress;

  /// Get unsent message count
  Future<int> getUnsentMessageCount() async {
    try {
      final unsentMessages = await HiveChatService.getUnsentMessages();
      return unsentMessages.length;
    } catch (e) {
      debugPrint('❌ Error getting unsent message count: $e');
      return 0;
    }
  }

  /// Get sync queue status
  Future<Map<String, dynamic>> getSyncStatus() async {
    try {
      final unsentMessages = await HiveChatService.getUnsentMessages();
      final syncQueue = await HiveChatService.getSyncQueue();
      
      return {
        'unsentCount': unsentMessages.length,
        'syncQueueCount': syncQueue.length,
        'isSyncInProgress': _syncInProgress,
        'isOnline': _connectivityService.isConnected,
        'lastSyncAttempt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Force sync a specific message
  Future<bool> forceSyncMessage(String messageId) async {
    if (!_connectivityService.isConnected) return false;

    try {
      final localMessage = await HiveChatService.getMessage(messageId);
      if (localMessage == null) return false;

      await _syncSingleMessage(localMessage);
      return true;
    } catch (e) {
      debugPrint('❌ Error force syncing message $messageId: $e');
      return false;
    }
  }

  /// Store received message from Firebase
  Future<void> storeReceivedMessage(Message message, String chatId) async {
    try {
      final localMessage = LocalMessage.fromMessage(message, chatId);
      await HiveChatService.storeMessage(localMessage);
      
      debugPrint('📥 Stored received message: ${message.id}');
    } catch (e) {
      debugPrint('❌ Error storing received message: $e');
    }
  }

  /// Handle message conflicts (if same message exists with different data)
  Future<void> resolveMessageConflict(LocalMessage localMessage, Message firebaseMessage) async {
    try {
      // Simple conflict resolution: Firebase version wins
      final updatedLocalMessage = LocalMessage.fromMessage(firebaseMessage, localMessage.chatId);
      await HiveChatService.updateMessage(updatedLocalMessage);
      
      debugPrint('🔄 Resolved message conflict for: ${localMessage.id}');
    } catch (e) {
      debugPrint('❌ Error resolving message conflict: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncTimer?.cancel();
    _initialized = false;
    debugPrint('📦 MessageSyncService disposed');
  }
}
