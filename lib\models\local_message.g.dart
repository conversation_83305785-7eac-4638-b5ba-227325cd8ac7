// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_message.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocalMessageAdapter extends TypeAdapter<LocalMessage> {
  @override
  final int typeId = 0;

  @override
  LocalMessage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalMessage(
      id: fields[0] as String,
      senderId: fields[1] as String,
      senderName: fields[2] as String,
      senderProfileUrl: fields[3] as String?,
      chatId: fields[4] as String,
      messageType: fields[5] as int,
      text: fields[6] as String?,
      mediaUrl: fields[7] as String?,
      localPath: fields[8] as String?,
      fileName: fields[9] as String?,
      fileSize: fields[10] as int?,
      thumbnailUrl: fields[11] as String?,
      localThumbnailPath: fields[12] as String?,
      messageStatus: fields[13] as int,
      timestamp: fields[14] as int,
      replyToMessageId: fields[15] as String?,
      replyToText: fields[16] as String?,
      replyToSenderName: fields[17] as String?,
      isForwarded: fields[18] as bool,
      readBy: (fields[19] as List).cast<String>(),
      deletedBy: (fields[20] as List).cast<String>(),
      metadataJson: fields[21] as String?,
      isSent: fields[22] as bool,
      isLocalOnly: fields[23] as bool,
      optimisticStatus: fields[24] as int?,
      uploadStatus: fields[25] as int?,
      uploadProgress: fields[26] as double?,
      createdAt: fields[27] as int,
      updatedAt: fields[28] as int,
    );
  }

  @override
  void write(BinaryWriter writer, LocalMessage obj) {
    writer
      ..writeByte(29)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.senderId)
      ..writeByte(2)
      ..write(obj.senderName)
      ..writeByte(3)
      ..write(obj.senderProfileUrl)
      ..writeByte(4)
      ..write(obj.chatId)
      ..writeByte(5)
      ..write(obj.messageType)
      ..writeByte(6)
      ..write(obj.text)
      ..writeByte(7)
      ..write(obj.mediaUrl)
      ..writeByte(8)
      ..write(obj.localPath)
      ..writeByte(9)
      ..write(obj.fileName)
      ..writeByte(10)
      ..write(obj.fileSize)
      ..writeByte(11)
      ..write(obj.thumbnailUrl)
      ..writeByte(12)
      ..write(obj.localThumbnailPath)
      ..writeByte(13)
      ..write(obj.messageStatus)
      ..writeByte(14)
      ..write(obj.timestamp)
      ..writeByte(15)
      ..write(obj.replyToMessageId)
      ..writeByte(16)
      ..write(obj.replyToText)
      ..writeByte(17)
      ..write(obj.replyToSenderName)
      ..writeByte(18)
      ..write(obj.isForwarded)
      ..writeByte(19)
      ..write(obj.readBy)
      ..writeByte(20)
      ..write(obj.deletedBy)
      ..writeByte(21)
      ..write(obj.metadataJson)
      ..writeByte(22)
      ..write(obj.isSent)
      ..writeByte(23)
      ..write(obj.isLocalOnly)
      ..writeByte(24)
      ..write(obj.optimisticStatus)
      ..writeByte(25)
      ..write(obj.uploadStatus)
      ..writeByte(26)
      ..write(obj.uploadProgress)
      ..writeByte(27)
      ..write(obj.createdAt)
      ..writeByte(28)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalMessageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
