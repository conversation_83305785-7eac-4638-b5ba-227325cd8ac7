import 'dart:io';
import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/local_storage_service.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:mr_garments_mobile/services/connectivity_service.dart';

/// Background upload service for WhatsApp-like image uploading
/// Handles upload queue, retry logic, status updates, and persistence
/// Ensures uploads continue even when app is in background or killed
class BackgroundUploadService {
  static BackgroundUploadService? _instance;
  static BackgroundUploadService get instance => _instance ??= BackgroundUploadService._();
  BackgroundUploadService._();

  final Queue<UploadTask> _uploadQueue = Queue<UploadTask>();
  final Map<String, UploadTask> _activeUploads = {};
  final Map<String, StreamController<UploadProgress>> _progressControllers = {};

  final ConnectivityService _connectivityService = ConnectivityService.instance;
  Timer? _backgroundProcessTimer;
  bool _isInitialized = false;

  bool _isProcessing = false;
  static const int maxConcurrentUploads =
      12; // Increased from 3 to 12 for faster uploads
  static const int maxRetries = 5; // Increased retries for better reliability
  static const String _uploadQueueBoxName = 'upload_queue';

  /// Initialize the background upload service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Restore pending uploads from Hive
      await _restorePendingUploads();

      // Set up background processing timer (runs every 10 seconds)
      _backgroundProcessTimer = Timer.periodic(
        const Duration(seconds: 10),
        (_) => _processQueueInBackground(),
      );

      // Listen to connectivity changes
      _connectivityService.connectionStatusStream.listen((isConnected) {
        if (isConnected) {
          debugPrint('🌐 Internet connected - resuming uploads');
          _processQueue();
        }
      });

      _isInitialized = true;
      debugPrint('✅ BackgroundUploadService initialized');
    } catch (e) {
      debugPrint('❌ Error initializing BackgroundUploadService: $e');
    }
  }

  /// Process queue in background (called periodically)
  Future<void> _processQueueInBackground() async {
    if (!_connectivityService.isConnected) return;
    if (_uploadQueue.isEmpty && _activeUploads.isEmpty) return;

    debugPrint(
      '🔄 Background processing: ${_uploadQueue.length} queued, ${_activeUploads.length} active',
    );
    await _processQueue();
  }

  /// Restore pending uploads from Hive storage
  Future<void> _restorePendingUploads() async {
    try {
      final box = await Hive.openBox(_uploadQueueBoxName);
      final pendingTasks = box.values.toList();

      debugPrint(
        '📦 Restoring ${pendingTasks.length} pending uploads from storage',
      );

      for (final taskData in pendingTasks) {
        try {
          final task = UploadTask.fromMap(taskData as Map<dynamic, dynamic>);

          // Check if local file still exists
          if (await LocalStorageService.localFileExists(task.localImagePath)) {
            _uploadQueue.add(task);
            debugPrint('✅ Restored upload task: ${task.messageId}');
          } else {
            // Remove from storage if file doesn't exist
            await box.delete(task.messageId);
            debugPrint('⚠ Removed task with missing file: ${task.messageId}');
          }
        } catch (e) {
          debugPrint('❌ Error restoring task: $e');
        }
      }

      // Start processing if we have tasks
      if (_uploadQueue.isNotEmpty) {
        _processQueue();
      }
    } catch (e) {
      debugPrint('❌ Error restoring pending uploads: $e');
    }
  }

  /// Persist upload task to Hive storage
  Future<void> _persistUploadTask(UploadTask task) async {
    try {
      final box = await Hive.openBox(_uploadQueueBoxName);
      await box.put(task.messageId, task.toMap());
      debugPrint('💾 Persisted upload task: ${task.messageId}');
    } catch (e) {
      debugPrint('❌ Error persisting upload task: $e');
    }
  }

  /// Remove upload task from Hive storage
  Future<void> _removePersistedTask(String messageId) async {
    try {
      final box = await Hive.openBox(_uploadQueueBoxName);
      await box.delete(messageId);
      debugPrint('🗑 Removed persisted task: $messageId');
    } catch (e) {
      debugPrint('❌ Error removing persisted task: $e');
    }
  }

  /// Add upload task to queue
  Future<void> queueUpload({
    required String messageId,
    required String chatId,
    required String localImagePath,
    String? thumbnailPath,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    Map<String, dynamic>? metadata,
    Function(UploadProgress)? onProgress,
    Function(String uploadUrl)? onSuccess,
    Function(String error)? onError,
  }) async {
    final task = UploadTask(
      messageId: messageId,
      chatId: chatId,
      localImagePath: localImagePath,
      thumbnailPath: thumbnailPath,
      replyToMessageId: replyToMessageId,
      replyToText: replyToText,
      replyToSenderName: replyToSenderName,
      metadata: metadata,
      onProgress: onProgress,
      onSuccess: onSuccess,
      onError: onError,
    );

    _uploadQueue.add(task);

    // Persist task to storage for recovery after app restart
    await _persistUploadTask(task);

    _processQueue();
  }

  /// Process upload queue
  Future<void> _processQueue() async {
    if (_isProcessing || _uploadQueue.isEmpty) return;
    if (_activeUploads.length >= maxConcurrentUploads) return;

    _isProcessing = true;

    while (_uploadQueue.isNotEmpty &&
        _activeUploads.length < maxConcurrentUploads) {
      final task = _uploadQueue.removeFirst();
      _activeUploads[task.messageId] = task;
      _processUpload(task);
    }

    _isProcessing = false;
  }

  /// Process individual upload task
  Future<void> _processUpload(UploadTask task) async {
    try {
      // Create progress controller
      final progressController = StreamController<UploadProgress>.broadcast();
      _progressControllers[task.messageId] = progressController;

      // Update progress: Starting
      final startProgress = UploadProgress(
        messageId: task.messageId,
        status: UploadStatus.uploading,
        progress: 0.0,
      );
      progressController.add(startProgress);
      task.onProgress?.call(startProgress);

      // Check if local file exists
      if (!await LocalStorageService.localFileExists(task.localImagePath)) {
        throw Exception('Local file not found: ${task.localImagePath}');
      }

      // Upload to Firebase/backend
      final localFile = File(task.localImagePath);

      // Update progress: 50% (uploading)
      final uploadingProgress = UploadProgress(
        messageId: task.messageId,
        status: UploadStatus.uploading,
        progress: 0.5,
      );
      progressController.add(uploadingProgress);
      task.onProgress?.call(uploadingProgress);

      // Perform actual upload using existing ChatService
      final uploadUrl = await ChatService.uploadFile(localFile, 'images');

      // Update progress: 100% (completed)
      final completedProgress = UploadProgress(
        messageId: task.messageId,
        status: UploadStatus.completed,
        progress: 1.0,
        uploadUrl: uploadUrl,
      );
      progressController.add(completedProgress);
      task.onProgress?.call(completedProgress);
      task.onSuccess?.call(uploadUrl);

      // Remove from persistent storage on success
      await _removePersistedTask(task.messageId);

      debugPrint('✅ Upload completed for message: ${task.messageId}');
    } catch (e) {
      debugPrint('❌ Upload failed for message ${task.messageId}: $e');

      // Retry logic
      if (task.retryCount < maxRetries) {
        task.retryCount++;
        debugPrint(
          '🔄 Retrying upload for message ${task.messageId} (attempt ${task.retryCount}/${maxRetries})',
        );

        // Add delay before retry (exponential backoff)
        await Future.delayed(Duration(seconds: task.retryCount * 3));

        // Update persisted task with new retry count
        await _persistUploadTask(task);

        // Re-queue the task
        _uploadQueue.addFirst(task);
      } else {
        // Max retries reached, mark as failed but keep in storage for manual retry
        debugPrint('⚠ Max retries reached for message ${task.messageId}');

        final failedProgress = UploadProgress(
          messageId: task.messageId,
          status: UploadStatus.failed,
          progress: 0.0,
          error: e.toString(),
        );

        final progressController = _progressControllers[task.messageId];
        progressController?.add(failedProgress);
        task.onProgress?.call(failedProgress);
        task.onError?.call(e.toString());

        // Keep in storage for potential manual retry
        await _persistUploadTask(task);
      }
    } finally {
      // Clean up
      _activeUploads.remove(task.messageId);
      _progressControllers[task.messageId]?.close();
      _progressControllers.remove(task.messageId);

      // Process next items in queue
      _processQueue();
    }
  }

  /// Dispose the service and clean up resources
  Future<void> dispose() async {
    _backgroundProcessTimer?.cancel();
    _backgroundProcessTimer = null;

    for (final controller in _progressControllers.values) {
      controller.close();
    }
    _progressControllers.clear();

    _isInitialized = false;
    debugPrint('🔄 BackgroundUploadService disposed');
  }

  /// Get upload progress stream for a message
  Stream<UploadProgress>? getUploadProgress(String messageId) {
    return _progressControllers[messageId]?.stream;
  }

  /// Check if message is currently uploading
  bool isUploading(String messageId) {
    return _activeUploads.containsKey(messageId);
  }

  /// Get current upload status for a message
  UploadStatus? getUploadStatus(String messageId) {
    if (_activeUploads.containsKey(messageId)) {
      return UploadStatus.uploading;
    }
    return null;
  }

  /// Cancel upload for a specific message
  Future<void> cancelUpload(String messageId) async {
    try {
      // Remove from queue
      _uploadQueue.removeWhere((task) => task.messageId == messageId);

      // Remove from active uploads
      _activeUploads.remove(messageId);

      // Close progress controller
      _progressControllers[messageId]?.close();
      _progressControllers.remove(messageId);

      debugPrint('Upload cancelled for message: $messageId');
    } catch (e) {
      debugPrint('Error cancelling upload: $e');
    }
  }

  /// Get all active upload tasks
  List<UploadTask> getActiveUploads() {
    return _activeUploads.values.toList();
  }

  /// Get queued upload tasks
  List<UploadTask> getQueuedUploads() {
    return _uploadQueue.toList();
  }

  /// Clear all uploads (for logout or reset)
  Future<void> clearAllUploads() async {
    try {
      _uploadQueue.clear();
      _activeUploads.clear();

      // Close all progress controllers
      for (final controller in _progressControllers.values) {
        controller.close();
      }
      _progressControllers.clear();

      debugPrint('All uploads cleared');
    } catch (e) {
      debugPrint('Error clearing uploads: $e');
    }
  }

  /// Retry failed upload
  Future<void> retryUpload(String messageId) async {
    try {
      // Find the task in failed state and re-queue it
      // This would typically be called from UI when user taps retry
      debugPrint('Retry upload requested for message: $messageId');
      // Implementation depends on how you store failed tasks
    } catch (e) {
      debugPrint('Error retrying upload: $e');
    }
  }
}

/// Upload task model
class UploadTask {
  final String messageId;
  final String chatId;
  final String localImagePath;
  final String? thumbnailPath;
  final String? replyToMessageId;
  final String? replyToText;
  final String? replyToSenderName;
  final Map<String, dynamic>? metadata;
  final Function(UploadProgress)? onProgress;
  final Function(String uploadUrl)? onSuccess;
  final Function(String error)? onError;

  int retryCount = 0;
  DateTime createdAt = DateTime.now();

  UploadTask({
    required this.messageId,
    required this.chatId,
    required this.localImagePath,
    this.thumbnailPath,
    this.replyToMessageId,
    this.replyToText,
    this.replyToSenderName,
    this.metadata,
    this.onProgress,
    this.onSuccess,
    this.onError,
    int? retryCount,
    DateTime? createdAt,
  }) {
    if (retryCount != null) this.retryCount = retryCount;
    if (createdAt != null) this.createdAt = createdAt;
  }

  /// Convert to Map for Hive storage
  Map<String, dynamic> toMap() {
    return {
      'messageId': messageId,
      'chatId': chatId,
      'localImagePath': localImagePath,
      'thumbnailPath': thumbnailPath,
      'replyToMessageId': replyToMessageId,
      'replyToText': replyToText,
      'replyToSenderName': replyToSenderName,
      'metadata': metadata,
      'retryCount': retryCount,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Create from Map (for Hive restoration)
  factory UploadTask.fromMap(Map<dynamic, dynamic> map) {
    return UploadTask(
      messageId: map['messageId'] as String,
      chatId: map['chatId'] as String,
      localImagePath: map['localImagePath'] as String,
      thumbnailPath: map['thumbnailPath'] as String?,
      replyToMessageId: map['replyToMessageId'] as String?,
      replyToText: map['replyToText'] as String?,
      replyToSenderName: map['replyToSenderName'] as String?,
      metadata:
          map['metadata'] != null
              ? Map<String, dynamic>.from(map['metadata'] as Map)
              : null,
      retryCount: map['retryCount'] as int? ?? 0,
      createdAt:
          map['createdAt'] != null
              ? DateTime.parse(map['createdAt'] as String)
              : DateTime.now(),
    );
  }
}

/// Upload progress model
class UploadProgress {
  final String messageId;
  final UploadStatus status;
  final double progress; // 0.0 to 1.0
  final String? uploadUrl;
  final String? error;

  UploadProgress({
    required this.messageId,
    required this.status,
    required this.progress,
    this.uploadUrl,
    this.error,
  });
}

/// Upload status enum
enum UploadStatus { pending, uploading, completed, failed, cancelled }

/// Extension to get user-friendly status text
extension UploadStatusExtension on UploadStatus {
  String get displayText {
    switch (this) {
      case UploadStatus.pending:
        return 'Pending';
      case UploadStatus.uploading:
        return 'Uploading';
      case UploadStatus.completed:
        return 'Sent';
      case UploadStatus.failed:
        return 'Failed';
      case UploadStatus.cancelled:
        return 'Cancelled';
    }
  }

  IconData get icon {
    switch (this) {
      case UploadStatus.pending:
        return Icons.schedule;
      case UploadStatus.uploading:
        return Icons.cloud_upload;
      case UploadStatus.completed:
        return Icons.check;
      case UploadStatus.failed:
        return Icons.error;
      case UploadStatus.cancelled:
        return Icons.cancel;
    }
  }
}